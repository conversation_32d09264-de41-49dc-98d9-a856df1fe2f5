# UAUI Core Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=0.0.0.0
FRONTEND_URL=http://localhost:5173

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=uaui_core
DB_USER=postgres
DB_PASSWORD=
DB_SSL=false
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100

# Security Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key_here
API_KEY_SALT=your_unique_salt_for_api_keys_here

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# LLM Provider API Keys
OPENAI_API_KEY=sk-your_openai_api_key_here
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here
GROQ_API_KEY=gsk_your_groq_api_key_here
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# Tool Service API Keys
SERPER_API_KEY=your_serper_api_key_for_web_search
SERPAPI_API_KEY=your_serpapi_key_alternative_search
OPENWEATHER_API_KEY=your_openweathermap_api_key
WEATHERAPI_KEY=your_weatherapi_com_key

# External Service Configuration
WEB_SEARCH_PROVIDER=serper
WEATHER_PROVIDER=openweather
SEARCH_RESULTS_LIMIT=10
WEATHER_UNITS=metric

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/uaui-core.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# HTTPS Configuration (Production)
HTTPS_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=
FORCE_HTTPS=false

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# Session Configuration
SESSION_TIMEOUT=3600
SESSION_CLEANUP_INTERVAL=300
MAX_SESSIONS_PER_USER=10

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=.txt,.json,.csv,.md
UPLOAD_DIR=uploads

# Monitoring and Health
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
PERFORMANCE_MONITORING=false

# Development Tools
VITE_TEMPO=false
DEBUG_MODE=false
ENABLE_SWAGGER=false

# Supabase Configuration (Optional)
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_PROJECT_ID=

# Email Configuration (Future use)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Analytics and Tracking (Optional)
ANALYTICS_ENABLED=false
GOOGLE_ANALYTICS_ID=
MIXPANEL_TOKEN=

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups

# Feature Flags
ENABLE_OAUTH=false
ENABLE_WEBSOCKETS=true
ENABLE_STREAMING=true
ENABLE_TOOL_EXECUTION=true
ENABLE_AGENT_MEMORY=true
