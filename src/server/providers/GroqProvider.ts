import { Base<PERSON><PERSON>rovider, LLMCompletionRequest, LLMCompletionResponse, LLMStreamChunk } from "./BaseLLMProvider";

export class GroqProvider extends BaseLLMProvider {
  constructor(apiKey: string) {
    super(apiKey, "https://api.groq.com/openai/v1", "llama3-8b-8192");
  }

  getName(): string {
    return "groq";
  }

  getAvailableModels(): string[] {
    return [
      "llama-3.1-405b-reasoning",
      "llama-3.1-70b-versatile",
      "llama-3.1-8b-instant",
      "llama3-groq-70b-8192-tool-use-preview",
      "llama3-groq-8b-8192-tool-use-preview",
      "llama3-70b-8192",
      "llama3-8b-8192",
      "mixtral-8x7b-32768",
      "gemma-7b-it",
      "gemma2-9b-it"
    ];
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeRequest(
        `${this.baseUrl}/models`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }
      );

      await response.json();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: this.handleError(error, "connection test").message,
      };
    }
  }

  async createCompletion(request: LLMCompletionRequest): Promise<LLMCompletionResponse> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const payload = {
        model: request.model || this.defaultModel,
        messages: request.messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: false,
        ...(request.tools && { tools: request.tools }),
      };

      const response = await this.makeRequest(
        `${this.baseUrl}/chat/completions`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('No completion choices returned');
      }

      const choice = data.choices[0];
      const result: LLMCompletionResponse = {
        id: data.id || this.generateId(),
        content: choice.message?.content || '',
        model: data.model || payload.model,
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        } : undefined,
        finishReason: choice.finish_reason,
      };

      this.logResponse(result);
      return result;
    } catch (error) {
      throw this.handleError(error, "completion");
    }
  }

  async createStreamingCompletion(
    request: LLMCompletionRequest,
    onChunk: (chunk: LLMStreamChunk) => void
  ): Promise<void> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const payload = {
        model: request.model || this.defaultModel,
        messages: request.messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: true,
        ...(request.tools && { tools: request.tools }),
      };

      const completionId = this.generateId();
      let fullContent = '';

      await this.makeStreamingRequest(
        `${this.baseUrl}/chat/completions`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(payload),
        },
        (line: string) => {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              onChunk({
                id: completionId,
                content: '',
                isComplete: true,
                finishReason: 'stop',
              });
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                onChunk({
                  id: completionId,
                  content,
                  isComplete: false,
                });
              }

              if (choice?.finish_reason) {
                onChunk({
                  id: completionId,
                  content: '',
                  isComplete: true,
                  finishReason: choice.finish_reason,
                });
              }
            } catch (parseError) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      );

      this.logResponse({
        id: completionId,
        content: fullContent,
        model: payload.model,
      });
    } catch (error) {
      throw this.handleError(error, "streaming completion");
    }
  }
}
