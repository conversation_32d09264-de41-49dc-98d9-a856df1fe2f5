import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface LLMMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

export interface LLMCompletionRequest {
  messages: LLMMessage[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  stream?: boolean;
  tools?: any[];
}

export interface LLMCompletionResponse {
  id: string;
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
}

export interface LLMStreamChunk {
  id: string;
  content: string;
  isComplete: boolean;
  finishReason?: string;
}

export abstract class BaseLLMProvider {
  protected apiKey: string;
  protected baseUrl: string;
  protected defaultModel: string;
  protected maxRetries: number = 3;
  protected retryDelay: number = 1000;

  constructor(apiKey: string, baseUrl: string, defaultModel: string) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.defaultModel = defaultModel;
  }

  abstract getName(): string;
  abstract getAvailableModels(): string[];
  abstract createCompletion(request: LLMCompletionRequest): Promise<LLMCompletionResponse>;
  abstract createStreamingCompletion(
    request: LLMCompletionRequest,
    onChunk: (chunk: LLMStreamChunk) => void
  ): Promise<void>;
  abstract testConnection(): Promise<{ success: boolean; error?: string }>;

  protected async makeRequest(
    url: string,
    options: RequestInit,
    retryCount: number = 0
  ): Promise<Response> {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return response;
    } catch (error) {
      if (retryCount < this.maxRetries) {
        logger.warn(`Request failed, retrying (${retryCount + 1}/${this.maxRetries}):`, error.message);
        await this.delay(this.retryDelay * Math.pow(2, retryCount));
        return this.makeRequest(url, options, retryCount + 1);
      }
      throw error;
    }
  }

  protected async makeStreamingRequest(
    url: string,
    options: RequestInit,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body reader available');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            onChunk(line);
          }
        }
      }

      // Process any remaining buffer
      if (buffer.trim()) {
        onChunk(buffer);
      }
    } finally {
      reader.releaseLock();
    }
  }

  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected generateId(): string {
    return `${this.getName()}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  protected validateRequest(request: LLMCompletionRequest): void {
    if (!request.messages || request.messages.length === 0) {
      throw new Error('Messages array is required and cannot be empty');
    }

    for (const message of request.messages) {
      if (!message.role || !message.content) {
        throw new Error('Each message must have role and content');
      }
      if (!['system', 'user', 'assistant'].includes(message.role)) {
        throw new Error('Message role must be system, user, or assistant');
      }
    }

    if (request.maxTokens && (request.maxTokens < 1 || request.maxTokens > 32000)) {
      throw new Error('maxTokens must be between 1 and 32000');
    }

    if (request.temperature && (request.temperature < 0 || request.temperature > 2)) {
      throw new Error('temperature must be between 0 and 2');
    }
  }

  protected logRequest(request: LLMCompletionRequest): void {
    logger.debug(`${this.getName()} request:`, {
      model: request.model || this.defaultModel,
      messageCount: request.messages.length,
      maxTokens: request.maxTokens,
      temperature: request.temperature,
      stream: request.stream,
    });
  }

  protected logResponse(response: LLMCompletionResponse): void {
    logger.debug(`${this.getName()} response:`, {
      id: response.id,
      model: response.model,
      contentLength: response.content.length,
      usage: response.usage,
      finishReason: response.finishReason,
    });
  }

  protected handleError(error: any, context: string): Error {
    const errorMessage = error.message || 'Unknown error';
    logger.error(`${this.getName()} ${context} error:`, error);
    
    // Standardize error messages
    if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
      return new Error('Invalid API key or unauthorized access');
    }
    if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
      return new Error('Rate limit exceeded, please try again later');
    }
    if (errorMessage.includes('400') || errorMessage.includes('bad request')) {
      return new Error('Invalid request parameters');
    }
    if (errorMessage.includes('500') || errorMessage.includes('internal server')) {
      return new Error('Provider internal server error');
    }
    if (errorMessage.includes('timeout') || errorMessage.includes('ECONNRESET')) {
      return new Error('Request timeout or connection reset');
    }

    return new Error(`${this.getName()} error: ${errorMessage}`);
  }
}
