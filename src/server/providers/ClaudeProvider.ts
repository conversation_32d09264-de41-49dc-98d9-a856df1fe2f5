import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LLMCompletionRequest, LLMCompletionResponse, LLMStreamChunk, LLMMessage } from "./BaseLLMProvider";

export class <PERSON><PERSON>rovider extends BaseLL<PERSON>rovider {
  constructor(apiKey: string) {
    super(apiKey, "https://api.anthropic.com/v1", "claude-3-haiku-20240307");
  }

  getName(): string {
    return "claude";
  }

  getAvailableModels(): string[] {
    return [
      "claude-3-5-sonnet-20241022",
      "claude-3-opus-20240229",
      "claude-3-sonnet-20240229",
      "claude-3-haiku-20240307"
    ];
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      // Test with a minimal message
      const response = await this.makeRequest(
        `${this.baseUrl}/messages`,
        {
          method: 'POST',
          headers: {
            'x-api-key': this.apiKey,
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify({
            model: this.defaultModel,
            max_tokens: 1,
            messages: [{ role: 'user', content: 'test' }],
          }),
        }
      );

      await response.json();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: this.handleError(error, "connection test").message,
      };
    }
  }

  async createCompletion(request: LLMCompletionRequest): Promise<LLMCompletionResponse> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const { systemMessage, userMessages } = this.formatMessages(request.messages);

      const payload: any = {
        model: request.model || this.defaultModel,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        messages: userMessages,
        stream: false,
      };

      if (systemMessage) {
        payload.system = systemMessage;
      }

      const response = await this.makeRequest(
        `${this.baseUrl}/messages`,
        {
          method: 'POST',
          headers: {
            'x-api-key': this.apiKey,
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (!data.content || data.content.length === 0) {
        throw new Error('No content returned from Claude');
      }

      const content = data.content
        .filter((item: any) => item.type === 'text')
        .map((item: any) => item.text)
        .join('');

      const result: LLMCompletionResponse = {
        id: data.id || this.generateId(),
        content,
        model: data.model || payload.model,
        usage: data.usage ? {
          promptTokens: data.usage.input_tokens,
          completionTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens,
        } : undefined,
        finishReason: data.stop_reason,
      };

      this.logResponse(result);
      return result;
    } catch (error) {
      throw this.handleError(error, "completion");
    }
  }

  async createStreamingCompletion(
    request: LLMCompletionRequest,
    onChunk: (chunk: LLMStreamChunk) => void
  ): Promise<void> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const { systemMessage, userMessages } = this.formatMessages(request.messages);

      const payload: any = {
        model: request.model || this.defaultModel,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        messages: userMessages,
        stream: true,
      };

      if (systemMessage) {
        payload.system = systemMessage;
      }

      const completionId = this.generateId();
      let fullContent = '';

      await this.makeStreamingRequest(
        `${this.baseUrl}/messages`,
        {
          method: 'POST',
          headers: {
            'x-api-key': this.apiKey,
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify(payload),
        },
        (line: string) => {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                const content = parsed.delta.text;
                fullContent += content;
                
                onChunk({
                  id: completionId,
                  content,
                  isComplete: false,
                });
              }

              if (parsed.type === 'message_stop') {
                onChunk({
                  id: completionId,
                  content: '',
                  isComplete: true,
                  finishReason: 'stop',
                });
              }
            } catch (parseError) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      );

      this.logResponse({
        id: completionId,
        content: fullContent,
        model: payload.model,
      });
    } catch (error) {
      throw this.handleError(error, "streaming completion");
    }
  }

  private formatMessages(messages: LLMMessage[]): { systemMessage?: string; userMessages: any[] } {
    let systemMessage: string | undefined;
    const userMessages: any[] = [];

    for (const message of messages) {
      if (message.role === 'system') {
        // Claude expects system message as a separate field
        systemMessage = message.content;
      } else {
        userMessages.push({
          role: message.role,
          content: message.content,
        });
      }
    }

    return { systemMessage, userMessages };
  }
}
