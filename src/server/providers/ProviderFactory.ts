import { <PERSON><PERSON><PERSON>rovider } from "./BaseLLMProvider";
import { OpenAIProvider } from "./OpenAIProvider";
import { <PERSON><PERSON><PERSON>ider } from "./ClaudeProvider";
import { GroqProvider } from "./GroqProvider";
import { OpenRouterProvider } from "./OpenRouterProvider";
import { ConfigManager } from "../utils/ConfigManager";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export class ProviderFactory {
  private static instance: ProviderFactory;
  private providers: Map<string, BaseLLMProvider> = new Map();

  private constructor() {
    this.initializeProviders();
  }

  public static getInstance(): ProviderFactory {
    if (!ProviderFactory.instance) {
      ProviderFactory.instance = new ProviderFactory();
    }
    return ProviderFactory.instance;
  }

  private initializeProviders(): void {
    // Initialize OpenAI provider
    const openaiKey = config.getProviderApiKey("openai");
    if (openaiKey) {
      this.providers.set("openai", new OpenAIProvider(openaiKey));
      logger.info("OpenAI provider initialized");
    }

    // Initialize Claude provider
    const claudeKey = config.getProviderApiKey("claude");
    if (claudeKey) {
      this.providers.set("claude", new ClaudeProvider(claudeKey));
      logger.info("Claude provider initialized");
    }

    // Initialize Groq provider
    const groqKey = config.getProviderApiKey("groq");
    if (groqKey) {
      this.providers.set("groq", new GroqProvider(groqKey));
      logger.info("Groq provider initialized");
    }

    // Initialize OpenRouter provider
    const openrouterKey = config.getProviderApiKey("openrouter");
    if (openrouterKey) {
      this.providers.set("openrouter", new OpenRouterProvider(openrouterKey));
      logger.info("OpenRouter provider initialized");
    }

    if (this.providers.size === 0) {
      logger.warn("No LLM providers initialized. Check your API key configuration.");
    } else {
      logger.info(`Initialized ${this.providers.size} LLM providers: ${Array.from(this.providers.keys()).join(", ")}`);
    }
  }

  public getProvider(name: string): BaseLLMProvider | null {
    return this.providers.get(name.toLowerCase()) || null;
  }

  public getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  public getAllProviders(): Map<string, BaseLLMProvider> {
    return new Map(this.providers);
  }

  public async testProvider(name: string): Promise<{ success: boolean; error?: string }> {
    const provider = this.getProvider(name);
    if (!provider) {
      return {
        success: false,
        error: `Provider ${name} not found or not configured`,
      };
    }

    try {
      return await provider.testConnection();
    } catch (error) {
      logger.error(`Failed to test provider ${name}:`, error);
      return {
        success: false,
        error: error.message || "Unknown error occurred",
      };
    }
  }

  public async testAllProviders(): Promise<Record<string, { success: boolean; error?: string }>> {
    const results: Record<string, { success: boolean; error?: string }> = {};
    
    for (const [name, provider] of this.providers) {
      try {
        results[name] = await provider.testConnection();
      } catch (error) {
        results[name] = {
          success: false,
          error: error.message || "Unknown error occurred",
        };
      }
    }

    return results;
  }

  public getProviderModels(name: string): string[] {
    const provider = this.getProvider(name);
    return provider ? provider.getAvailableModels() : [];
  }

  public getAllProviderModels(): Record<string, string[]> {
    const models: Record<string, string[]> = {};
    
    for (const [name, provider] of this.providers) {
      models[name] = provider.getAvailableModels();
    }

    return models;
  }

  public refreshProviders(): void {
    logger.info("Refreshing LLM providers...");
    this.providers.clear();
    this.initializeProviders();
  }

  public addProvider(name: string, provider: BaseLLMProvider): void {
    this.providers.set(name.toLowerCase(), provider);
    logger.info(`Added provider: ${name}`);
  }

  public removeProvider(name: string): boolean {
    const removed = this.providers.delete(name.toLowerCase());
    if (removed) {
      logger.info(`Removed provider: ${name}`);
    }
    return removed;
  }

  public getProviderStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [name, provider] of this.providers) {
      stats[name] = {
        name: provider.getName(),
        models: provider.getAvailableModels(),
        available: true,
      };
    }

    return stats;
  }
}
