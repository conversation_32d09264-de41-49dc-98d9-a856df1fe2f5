import { BaseLL<PERSON>rovider, LLMCompletionRequest, LLMCompletionResponse, LLMStreamChunk } from "./BaseLLMProvider";

export class OpenAIProvider extends BaseLLMProvider {
  constructor(apiKey: string) {
    super(apiKey, "https://api.openai.com/v1", "gpt-3.5-turbo");
  }

  getName(): string {
    return "openai";
  }

  getAvailableModels(): string[] {
    return [
      "gpt-4o",
      "gpt-4o-mini",
      "gpt-4-turbo",
      "gpt-4",
      "gpt-3.5-turbo",
      "gpt-3.5-turbo-16k"
    ];
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeRequest(
        `${this.baseUrl}/models`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }
      );

      await response.json();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: this.handleError(error, "connection test").message,
      };
    }
  }

  async createCompletion(request: LLMCompletionRequest): Promise<LLMCompletionResponse> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const payload = {
        model: request.model || this.defaultModel,
        messages: request.messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: false,
        ...(request.tools && { tools: request.tools }),
      };

      const response = await this.makeRequest(
        `${this.baseUrl}/chat/completions`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (!data.choices || data.choices.length === 0) {
        throw new Error('No completion choices returned');
      }

      const choice = data.choices[0];
      const result: LLMCompletionResponse = {
        id: data.id || this.generateId(),
        content: choice.message?.content || '',
        model: data.model || payload.model,
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        } : undefined,
        finishReason: choice.finish_reason,
      };

      this.logResponse(result);
      return result;
    } catch (error) {
      throw this.handleError(error, "completion");
    }
  }

  async createStreamingCompletion(
    request: LLMCompletionRequest,
    onChunk: (chunk: LLMStreamChunk) => void
  ): Promise<void> {
    try {
      this.validateRequest(request);
      this.logRequest(request);

      const payload = {
        model: request.model || this.defaultModel,
        messages: request.messages,
        max_tokens: request.maxTokens || 1000,
        temperature: request.temperature || 0.7,
        stream: true,
        ...(request.tools && { tools: request.tools }),
      };

      const completionId = this.generateId();
      let fullContent = '';

      await this.makeStreamingRequest(
        `${this.baseUrl}/chat/completions`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify(payload),
        },
        (line: string) => {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              onChunk({
                id: completionId,
                content: '',
                isComplete: true,
                finishReason: 'stop',
              });
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const choice = parsed.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                onChunk({
                  id: completionId,
                  content,
                  isComplete: false,
                });
              }

              if (choice?.finish_reason) {
                onChunk({
                  id: completionId,
                  content: '',
                  isComplete: true,
                  finishReason: choice.finish_reason,
                });
              }
            } catch (parseError) {
              // Ignore parsing errors for malformed chunks
            }
          }
        }
      );

      this.logResponse({
        id: completionId,
        content: fullContent,
        model: payload.model,
      });
    } catch (error) {
      throw this.handleError(error, "streaming completion");
    }
  }
}
