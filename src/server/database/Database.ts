import { Pool, PoolClient } from "pg";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class Database {
  private static instance: Database;
  private pool: Pool;

  private constructor() {
    this.pool = new Pool({
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      database: process.env.DB_NAME || "uaui_core",
      user: process.env.DB_USER || "postgres",
      password: process.env.DB_PASSWORD || "password",
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    this.pool.on("error", (err) => {
      logger.error("Unexpected error on idle client:", err);
    });
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  async query(text: string, params?: any[]): Promise<any> {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;

      logger.debug("Executed query", {
        query: text.substring(0, 100) + (text.length > 100 ? "..." : ""),
        duration: `${duration}ms`,
        rows: result.rowCount,
      });

      return result;
    } catch (error) {
      logger.error("Database query error:", {
        query: text,
        params,
        error: error.message,
      });
      throw error;
    }
  }

  async getClient(): Promise<PoolClient> {
    return await this.pool.connect();
  }

  async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
  ): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query("BEGIN");
      const result = await callback(client);
      await client.query("COMMIT");
      return result;
    } catch (error) {
      await client.query("ROLLBACK");
      throw error;
    } finally {
      client.release();
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query("SELECT 1 as health");
      return result.rows[0].health === 1;
    } catch (error) {
      logger.error("Database health check failed:", error);
      return false;
    }
  }

  async getStats(): Promise<any> {
    try {
      const result = await this.query(`
        SELECT 
          (SELECT count(*) FROM agents WHERE is_active = true) as active_agents,
          (SELECT count(*) FROM tools WHERE is_active = true) as active_tools,
          (SELECT count(*) FROM providers WHERE enabled = true) as enabled_providers,
          (SELECT count(*) FROM agent_sessions WHERE is_active = true) as active_sessions
      `);

      return result.rows[0];
    } catch (error) {
      logger.error("Failed to get database stats:", error);
      return null;
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
    logger.info("Database connection pool closed");
  }
}
