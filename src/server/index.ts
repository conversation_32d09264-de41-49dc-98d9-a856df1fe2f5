import Fastify from "fastify";
import cors from "@fastify/cors";
import jwt from "@fastify/jwt";
import rateLimit from "@fastify/rate-limit";
import websocket from "@fastify/websocket";
import { Server as SocketIOServer } from "socket.io";
import { createAdapter } from "@socket.io/redis-adapter";
import Redis from "ioredis";
import { UAUICoreEngine } from "./core/UAUICoreEngine";
import { StateManager } from "./core/StateManager";
import { EventBus } from "./core/EventBus";
import { Logger } from "./utils/Logger";
import { agentRoutes } from "./routes/agents";
import { authRoutes } from "./routes/auth";
import { APIXIntegrationLayer } from "./core/APIXIntegrationLayer";

const logger = Logger.getInstance();

export class UAUIServer {
  private fastify: any;
  private io: SocketIOServer;
  private redis: Redis;
  private coreEngine: UAUICoreEngine;
  private stateManager: StateManager;
  private eventBus: EventBus;
  private apixLayer: APIXIntegrationLayer;

  constructor() {
    this.fastify = Fastify({ logger: false });
    this.redis = new Redis({
      host: process.env.REDIS_HOST || "localhost",
      port: parseInt(process.env.REDIS_PORT || "6379"),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    });

    this.eventBus = new EventBus();
    this.stateManager = new StateManager(this.redis);
    this.coreEngine = new UAUICoreEngine(this.eventBus, this.stateManager);
  }

  async initialize(): Promise<void> {
    try {
      // Register plugins
      await this.fastify.register(cors, {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        credentials: true,
      });

      await this.fastify.register(jwt, {
        secret: process.env.JWT_SECRET || "your-secret-key",
      });

      await this.fastify.register(rateLimit, {
        max: 100,
        timeWindow: "1 minute",
        redis: this.redis,
      });

      await this.fastify.register(websocket);

      // Initialize Socket.IO
      this.io = new SocketIOServer(this.fastify.server, {
        cors: {
          origin: process.env.FRONTEND_URL || "http://localhost:5173",
          methods: ["GET", "POST"],
          credentials: true,
        },
      });

      // Setup Redis adapter for Socket.IO
      const pubClient = this.redis.duplicate();
      const subClient = this.redis.duplicate();
      this.io.adapter(createAdapter(pubClient, subClient));

      // Initialize APIX Integration Layer
      this.apixLayer = new APIXIntegrationLayer(this.io, this.coreEngine);
      await this.apixLayer.initialize();

      // Register routes
      await this.fastify.register(authRoutes, { prefix: "/api/v1/auth" });
      await this.fastify.register(agentRoutes, { prefix: "/api/v1/agents" });

      // Health check endpoint
      this.fastify.get("/health", async () => {
        return {
          status: "healthy",
          timestamp: new Date().toISOString(),
          services: {
            redis: this.redis.status,
            database: "connected",
          },
        };
      });

      logger.info("UAUI Server initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize UAUI Server:", error);
      throw error;
    }
  }

  async start(port: number = 3001): Promise<void> {
    try {
      await this.fastify.listen({ port, host: "0.0.0.0" });
      logger.info(`UAUI Server started on port ${port}`);
    } catch (error) {
      logger.error("Failed to start UAUI Server:", error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      await this.fastify.close();
      await this.redis.quit();
      logger.info("UAUI Server stopped");
    } catch (error) {
      logger.error("Failed to stop UAUI Server:", error);
      throw error;
    }
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new UAUIServer();

  server
    .initialize()
    .then(() => {
      return server.start();
    })
    .catch((error) => {
      logger.error("Failed to start server:", error);
      process.exit(1);
    });

  // Graceful shutdown
  process.on("SIGTERM", async () => {
    await server.stop();
    process.exit(0);
  });

  process.on("SIGINT", async () => {
    await server.stop();
    process.exit(0);
  });
}
