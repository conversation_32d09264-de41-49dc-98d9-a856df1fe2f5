import Fastify from "fastify";
import cors from "@fastify/cors";
import jwt from "@fastify/jwt";
import rateLimit from "@fastify/rate-limit";
import websocket from "@fastify/websocket";
import { Server as SocketIOServer } from "socket.io";
import { createAdapter } from "@socket.io/redis-adapter";
import Redis from "ioredis";
import { UAUICoreEngine } from "./core/UAUICoreEngine";
import { StateManager } from "./core/StateManager";
import { EventBus } from "./core/EventBus";
import { Logger } from "./utils/Logger";
import { ConfigManager } from "./utils/ConfigManager";
import { SecurityMiddleware } from "./middleware/SecurityMiddleware";
import { agentRoutes } from "./routes/agents";
import { authRoutes } from "./routes/auth";
import { APIXIntegrationLayer } from "./core/APIXIntegrationLayer";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export class UAUIServer {
  private fastify: any;
  private io: SocketIOServer;
  private redis: Redis;
  private coreEngine: UAUICoreEngine;
  private stateManager: StateManager;
  private eventBus: EventBus;
  private apixLayer: APIXIntegrationLayer;

  constructor() {
    this.fastify = Fastify({
      logger: config.isDevelopment() ? true : false,
      trustProxy: config.isProduction()
    });

    this.redis = new Redis({
      host: config.get("REDIS_HOST"),
      port: config.get("REDIS_PORT"),
      password: config.get("REDIS_PASSWORD"),
      db: config.get("REDIS_DB"),
      retryDelayOnFailover: config.get("REDIS_RETRY_DELAY"),
      maxRetriesPerRequest: config.get("REDIS_MAX_RETRIES"),
    });

    this.eventBus = new EventBus();
    this.stateManager = new StateManager(this.redis);
    this.coreEngine = new UAUICoreEngine(this.eventBus, this.stateManager);
  }

  async initialize(): Promise<void> {
    try {
      // Register plugins
      await this.fastify.register(cors, {
        origin: config.get("CORS_ORIGIN").split(","),
        credentials: config.get("CORS_CREDENTIALS"),
        methods: config.get("CORS_METHODS").split(","),
        allowedHeaders: config.get("CORS_HEADERS").split(","),
      });

      await this.fastify.register(jwt, {
        secret: config.get("JWT_SECRET"),
      });

      await this.fastify.register(rateLimit, {
        max: config.get("RATE_LIMIT_MAX"),
        timeWindow: config.get("RATE_LIMIT_WINDOW"),
        redis: this.redis,
        skipSuccessfulRequests: config.get("RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS"),
        keyGenerator: SecurityMiddleware.createRateLimitKeyGenerator(),
      });

      await this.fastify.register(websocket);

      // Register security middleware
      if (config.get("FORCE_HTTPS")) {
        this.fastify.addHook("onRequest", SecurityMiddleware.enforceHTTPS);
      }

      this.fastify.addHook("onRequest", SecurityMiddleware.addSecurityHeaders);
      this.fastify.addHook("preHandler", SecurityMiddleware.sanitizeRequestBody);
      this.fastify.addHook("preHandler", SecurityMiddleware.validateRequestSize);
      this.fastify.addHook("preHandler", SecurityMiddleware.validateContentType);

      // Initialize Socket.IO
      this.io = new SocketIOServer(this.fastify.server, {
        cors: {
          origin: config.get("CORS_ORIGIN").split(","),
          methods: config.get("CORS_METHODS").split(","),
          credentials: config.get("CORS_CREDENTIALS"),
        },
      });

      // Setup Redis adapter for Socket.IO
      const pubClient = this.redis.duplicate();
      const subClient = this.redis.duplicate();
      this.io.adapter(createAdapter(pubClient, subClient));

      // Initialize APIX Integration Layer
      this.apixLayer = new APIXIntegrationLayer(this.io, this.coreEngine);
      await this.apixLayer.initialize();

      // Register routes
      await this.fastify.register(authRoutes, { prefix: "/api/v1/auth" });
      await this.fastify.register(agentRoutes, { prefix: "/api/v1/agents" });

      // Health check endpoint
      this.fastify.get("/health", async () => {
        return {
          status: "healthy",
          timestamp: new Date().toISOString(),
          services: {
            redis: this.redis.status,
            database: "connected",
          },
        };
      });

      logger.info("UAUI Server initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize UAUI Server:", error);
      throw error;
    }
  }

  async start(port?: number): Promise<void> {
    try {
      const serverPort = port || config.get("PORT");
      const serverHost = config.get("HOST");

      await this.fastify.listen({
        port: serverPort,
        host: serverHost
      });

      logger.info(`UAUI Server started on ${serverHost}:${serverPort}`);
      logger.info(`Environment: ${config.get("NODE_ENV")}`);
      logger.info(`Frontend URL: ${config.get("FRONTEND_URL")}`);
    } catch (error) {
      logger.error("Failed to start UAUI Server:", error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      await this.fastify.close();
      await this.redis.quit();
      logger.info("UAUI Server stopped");
    } catch (error) {
      logger.error("Failed to stop UAUI Server:", error);
      throw error;
    }
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new UAUIServer();

  server
    .initialize()
    .then(() => {
      return server.start();
    })
    .catch((error) => {
      logger.error("Failed to start server:", error);
      process.exit(1);
    });

  // Graceful shutdown
  process.on("SIGTERM", async () => {
    await server.stop();
    process.exit(0);
  });

  process.on("SIGINT", async () => {
    await server.stop();
    process.exit(0);
  });
}
