// APIX v2.0.0 Event Schema - SynapseAI Enhanced TypeScript Interfaces

export interface APIXEvent {
  id: string;
  type: string;
  data?: any;
  metadata?: {
    userId?: string;
    sessionId?: string;
    socketId?: string;
    timestamp?: string;
    tenantId?: string;
    agentId?: string;
    toolId?: string;
    [key: string]: any;
  };
  timestamp: string;
}

export interface APIXResponse {
  id: string;
  type: string;
  status: "success" | "failed" | "pending";
  data?: any;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// Real-time streaming events
export interface TextChunkEvent extends APIXEvent {
  type: "stream:text_chunk";
  data: {
    chunk: string;
    isComplete: boolean;
    agentId: string;
    sessionId: string;
    messageId: string;
  };
}

export interface StateUpdateEvent extends APIXEvent {
  type: "stream:state_update";
  data: {
    sessionId: string;
    agentId: string;
    state: Record<string, any>;
    context: Record<string, any>;
    memory?: Record<string, any>;
  };
}

export interface UserInputRequestEvent extends APIXEvent {
  type: "stream:request_user_input";
  data: {
    sessionId: string;
    agentId: string;
    prompt: string;
    inputType: "text" | "choice" | "file" | "confirmation";
    options?: string[];
    required: boolean;
    timeout?: number;
  };
}

export interface UserInputResponseEvent extends APIXEvent {
  type: "stream:user_input_response";
  data: {
    sessionId: string;
    agentId: string;
    requestId: string;
    response: any;
    responseType: "text" | "choice" | "file" | "confirmation";
  };
}

// Tool execution events
export interface ToolCallStartEvent extends APIXEvent {
  type: "tool:call_start";
  data: {
    toolId: string;
    toolName: string;
    parameters: Record<string, any>;
    agentId?: string;
    sessionId?: string;
    executionId: string;
  };
}

export interface ToolCallResultEvent extends APIXEvent {
  type: "tool:call_result";
  data: {
    toolId: string;
    toolName: string;
    executionId: string;
    result: any;
    executionTime: number;
    success: boolean;
  };
}

export interface ToolCallErrorEvent extends APIXEvent {
  type: "tool:call_error";
  data: {
    toolId: string;
    toolName: string;
    executionId: string;
    error: string;
    retryCount: number;
    willRetry: boolean;
  };
}

// Session management events
export interface SessionStartEvent extends APIXEvent {
  type: "session:start";
  data: {
    sessionId: string;
    agentId: string;
    userId?: string;
    configuration: Record<string, any>;
    ttl?: number;
  };
}

export interface SessionEndEvent extends APIXEvent {
  type: "session:end";
  data: {
    sessionId: string;
    agentId: string;
    reason: "timeout" | "user_ended" | "error" | "completed";
    duration: number;
    messageCount: number;
  };
}

// Agent execution events
export interface AgentExecuteEvent extends APIXEvent {
  type: "agent:execute";
  data: {
    agentId: string;
    sessionId: string;
    input: string;
    context?: Record<string, any>;
    streaming?: boolean;
  };
}

export interface AgentResponseEvent extends APIXEvent {
  type: "agent:response";
  data: {
    agentId: string;
    sessionId: string;
    response: string;
    isStreaming: boolean;
    isComplete: boolean;
    toolCalls?: any[];
    context: Record<string, any>;
  };
}

// Agent Events
export interface AgentCreateEvent extends APIXEvent {
  type: "agent:create";
  data: {
    name: string;
    description: string;
    provider: "openai" | "claude" | "gemini" | "mistral" | "groq";
    hasMemory: boolean;
    tools: string[];
    promptTemplate: string;
  };
}

export interface AgentUpdateEvent extends APIXEvent {
  type: "agent:update";
  data: {
    id: string;
    name?: string;
    description?: string;
    provider?: "openai" | "claude" | "gemini" | "mistral" | "groq";
    hasMemory?: boolean;
    tools?: string[];
    promptTemplate?: string;
  };
}

export interface AgentDeleteEvent extends APIXEvent {
  type: "agent:delete";
  data: {
    id: string;
  };
}

export interface AgentReadEvent extends APIXEvent {
  type: "agent:read";
  data: {
    id: string;
  };
}

export interface AgentListEvent extends APIXEvent {
  type: "agent:list";
  data?: {
    limit?: number;
    offset?: number;
    filter?: {
      provider?: string;
      hasMemory?: boolean;
    };
  };
}

// Tool Events
export interface ToolExecuteEvent extends APIXEvent {
  type: "tool:execute";
  data: {
    toolName: string;
    parameters: Record<string, any>;
    agentId?: string;
  };
}

export interface ToolListEvent extends APIXEvent {
  type: "tool:list";
  data?: {
    type?: "internal" | "external";
    active?: boolean;
  };
}

export interface ToolCreateEvent extends APIXEvent {
  type: "tool:create";
  data: {
    name: string;
    description: string;
    type: "internal" | "external";
    schema: string;
    endpoint?: string;
    active: boolean;
  };
}

export interface ToolUpdateEvent extends APIXEvent {
  type: "tool:update";
  data: {
    id: string;
    name?: string;
    description?: string;
    type?: "internal" | "external";
    schema?: string;
    endpoint?: string;
    active?: boolean;
  };
}

export interface ToolDeleteEvent extends APIXEvent {
  type: "tool:delete";
  data: {
    id: string;
  };
}

// Provider Events
export interface ProviderConfigureEvent extends APIXEvent {
  type: "provider:configure";
  data: {
    name: "openai" | "claude" | "gemini" | "mistral" | "groq";
    apiKey: string;
    enabled: boolean;
    priority: number;
    fallbackEnabled: boolean;
    maxRetries: number;
  };
}

export interface ProviderStatusEvent extends APIXEvent {
  type: "provider:status";
  data: {
    name: string;
  };
}

export interface ProviderTestEvent extends APIXEvent {
  type: "provider:test";
  data: {
    name: string;
  };
}

// WebSocket Events
export interface WSAgentSwitchEvent extends APIXEvent {
  type: "ws:agent:switch";
  data: {
    agentId: string;
    sessionId: string;
  };
}

export interface WSSystemStatusEvent extends APIXEvent {
  type: "ws:system:status";
  data?: {};
}

// System Events
export interface SystemStatusEvent extends APIXEvent {
  type: "system:status";
  data?: {};
}

export interface SystemHealthEvent extends APIXEvent {
  type: "system:health";
  data?: {};
}

// Response Types
export interface AgentResponse {
  id: string;
  name: string;
  description: string;
  provider: string;
  hasMemory: boolean;
  tools: string[];
  promptTemplate: string;
  createdAt: string;
  updatedAt: string;
}

export interface ToolResponse {
  id: string;
  name: string;
  description: string;
  type: "internal" | "external";
  schema: string;
  endpoint?: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProviderResponse {
  name: string;
  enabled: boolean;
  priority: number;
  fallbackEnabled: boolean;
  maxRetries: number;
  status: "online" | "offline" | "error" | "unknown";
  latency?: number;
  usageCount: number;
  errorRate: number;
  lastChecked: string;
}

export interface SystemStatusResponse {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  services: {
    redis: string;
    database: string;
    websocket: string;
    http: string;
  };
  metrics: {
    connectedClients: number;
    activeAgents: number;
    registeredTools: number;
    enabledProviders: number;
  };
}

// Error Types
export interface APIXError {
  code: string;
  message: string;
  details?: any;
}

// Enhanced session and hybrid agent events
export interface HybridAgentCreateEvent extends APIXEvent {
  type: "hybrid_agent:create";
  data: {
    name: string;
    description: string;
    agentConfig: {
      provider: string;
      promptTemplate: string;
      hasMemory: boolean;
    };
    toolConfigs: Array<{
      toolId: string;
      parameters: Record<string, any>;
      conditions?: Record<string, any>;
    }>;
    flowDefinition: Record<string, any>;
  };
}

export interface HybridAgentExecuteEvent extends APIXEvent {
  type: "hybrid_agent:execute";
  data: {
    hybridAgentId: string;
    sessionId: string;
    input: string;
    context?: Record<string, any>;
  };
}

// Knowledge base events
export interface KnowledgeBaseCreateEvent extends APIXEvent {
  type: "knowledge:create";
  data: {
    name: string;
    description: string;
    sources: Array<{
      type: "file" | "url" | "text";
      content: string;
      metadata?: Record<string, any>;
    }>;
    embeddingProvider: string;
  };
}

export interface KnowledgeBaseQueryEvent extends APIXEvent {
  type: "knowledge:query";
  data: {
    knowledgeBaseId: string;
    query: string;
    limit?: number;
    threshold?: number;
  };
}

// Widget generation events
export interface WidgetGenerateEvent extends APIXEvent {
  type: "widget:generate";
  data: {
    agentId?: string;
    toolId?: string;
    hybridAgentId?: string;
    widgetType: "js" | "iframe" | "plugin";
    configuration: Record<string, any>;
    styling?: Record<string, any>;
  };
}

// Analytics events
export interface AnalyticsTrackEvent extends APIXEvent {
  type: "analytics:track";
  data: {
    eventName: string;
    properties: Record<string, any>;
    userId?: string;
    sessionId?: string;
    agentId?: string;
    timestamp: string;
  };
}

// Union Types for Event Discrimination
export type AllAPIXEvents =
  | AgentCreateEvent
  | AgentUpdateEvent
  | AgentDeleteEvent
  | AgentReadEvent
  | AgentListEvent
  | AgentExecuteEvent
  | AgentResponseEvent
  | ToolExecuteEvent
  | ToolListEvent
  | ToolCreateEvent
  | ToolUpdateEvent
  | ToolDeleteEvent
  | ToolCallStartEvent
  | ToolCallResultEvent
  | ToolCallErrorEvent
  | ProviderConfigureEvent
  | ProviderStatusEvent
  | ProviderTestEvent
  | WSAgentSwitchEvent
  | WSSystemStatusEvent
  | SystemStatusEvent
  | SystemHealthEvent
  | TextChunkEvent
  | StateUpdateEvent
  | UserInputRequestEvent
  | UserInputResponseEvent
  | SessionStartEvent
  | SessionEndEvent
  | HybridAgentCreateEvent
  | HybridAgentExecuteEvent
  | KnowledgeBaseCreateEvent
  | KnowledgeBaseQueryEvent
  | WidgetGenerateEvent
  | AnalyticsTrackEvent;
