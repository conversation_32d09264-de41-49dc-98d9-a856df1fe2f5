import winston from "winston";

export class Logger {
  private static instance: winston.Logger;

  private constructor() {}

  public static getInstance(): winston.Logger {
    if (!Logger.instance) {
      Logger.instance = winston.createLogger({
        level: process.env.LOG_LEVEL || "info",
        format: winston.format.combine(
          winston.format.timestamp({
            format: "YYYY-MM-DD HH:mm:ss",
          }),
          winston.format.errors({ stack: true }),
          winston.format.colorize({ all: true }),
          winston.format.printf(
            ({ timestamp, level, message, stack, ...meta }) => {
              let log = `${timestamp} [${level}]: ${message}`;

              if (Object.keys(meta).length > 0) {
                log += ` ${JSON.stringify(meta)}`;
              }

              if (stack) {
                log += `\n${stack}`;
              }

              return log;
            },
          ),
        ),
        transports: [
          new winston.transports.Console({
            handleExceptions: true,
            handleRejections: true,
          }),
          new winston.transports.File({
            filename: "logs/error.log",
            level: "error",
            handleExceptions: true,
            handleRejections: true,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
          }),
          new winston.transports.File({
            filename: "logs/combined.log",
            handleExceptions: true,
            handleRejections: true,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
          }),
        ],
        exitOnError: false,
      });

      // Create logs directory if it doesn't exist
      const fs = require("fs");
      const path = require("path");
      const logsDir = path.join(process.cwd(), "logs");
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
    }

    return Logger.instance;
  }

  public static createChildLogger(service: string): winston.Logger {
    const parentLogger = Logger.getInstance();
    return parentLogger.child({ service });
  }
}
