import crypto from "crypto";
import { Database } from "../database/Database";
import { ConfigManager } from "./ConfigManager";
import { Logger } from "./Logger";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface ApiKeyData {
  id: string;
  name: string;
  keyHash: string;
  encryptedKey: string;
  userId: string;
  permissions: string[];
  rateLimit: number;
  expiresAt?: Date;
  lastUsed?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateApiKeyRequest {
  name: string;
  userId: string;
  permissions: string[];
  rateLimit?: number;
  expiresAt?: Date;
}

export class ApiKeyManager {
  private static instance: ApiKeyManager;
  private db: Database;

  private constructor() {
    this.db = Database.getInstance();
  }

  public static getInstance(): ApiKeyManager {
    if (!ApiKeyManager.instance) {
      ApiKeyManager.instance = new ApiKeyManager();
    }
    return ApiKeyManager.instance;
  }

  // Generate a new API key
  public async createApiKey(request: CreateApiKeyRequest): Promise<{ apiKey: string; keyData: ApiKeyData }> {
    try {
      // Generate a secure API key
      const apiKey = this.generateSecureApiKey();
      const keyHash = this.hashApiKey(apiKey);
      const encryptedKey = config.encryptApiKey(apiKey);

      const result = await this.db.query(
        `
        INSERT INTO api_keys (name, key_hash, encrypted_key, user_id, permissions, rate_limit, expires_at, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
        `,
        [
          request.name,
          keyHash,
          encryptedKey,
          request.userId,
          JSON.stringify(request.permissions),
          request.rateLimit || 1000,
          request.expiresAt,
          true,
        ]
      );

      const keyData = this.mapDbRowToApiKey(result.rows[0]);

      logger.info(`API key created: ${request.name} for user ${request.userId}`);

      return { apiKey, keyData };
    } catch (error) {
      logger.error('Failed to create API key:', error);
      throw new Error('Failed to create API key');
    }
  }

  // Validate an API key
  public async validateApiKey(apiKey: string): Promise<ApiKeyData | null> {
    try {
      const keyHash = this.hashApiKey(apiKey);

      const result = await this.db.query(
        `
        SELECT * FROM api_keys 
        WHERE key_hash = $1 AND is_active = true
        `,
        [keyHash]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const keyData = this.mapDbRowToApiKey(result.rows[0]);

      // Check if key is expired
      if (keyData.expiresAt && keyData.expiresAt < new Date()) {
        await this.deactivateApiKey(keyData.id);
        return null;
      }

      // Update last used timestamp
      await this.updateLastUsed(keyData.id);

      return keyData;
    } catch (error) {
      logger.error('Failed to validate API key:', error);
      return null;
    }
  }

  // Revoke an API key
  public async revokeApiKey(keyId: string, userId: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        `
        UPDATE api_keys 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2
        `,
        [keyId, userId]
      );

      const success = result.rowCount > 0;
      if (success) {
        logger.info(`API key revoked: ${keyId} by user ${userId}`);
      }

      return success;
    } catch (error) {
      logger.error('Failed to revoke API key:', error);
      return false;
    }
  }

  // List API keys for a user
  public async listApiKeys(userId: string): Promise<Omit<ApiKeyData, 'encryptedKey'>[]> {
    try {
      const result = await this.db.query(
        `
        SELECT id, name, key_hash, user_id, permissions, rate_limit, expires_at, last_used, is_active, created_at, updated_at
        FROM api_keys 
        WHERE user_id = $1
        ORDER BY created_at DESC
        `,
        [userId]
      );

      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        keyHash: row.key_hash,
        userId: row.user_id,
        permissions: JSON.parse(row.permissions || '[]'),
        rateLimit: row.rate_limit,
        expiresAt: row.expires_at,
        lastUsed: row.last_used,
        isActive: row.is_active,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }));
    } catch (error) {
      logger.error('Failed to list API keys:', error);
      throw new Error('Failed to list API keys');
    }
  }

  // Update API key permissions
  public async updateApiKeyPermissions(keyId: string, userId: string, permissions: string[]): Promise<boolean> {
    try {
      const result = await this.db.query(
        `
        UPDATE api_keys 
        SET permissions = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND user_id = $3
        `,
        [JSON.stringify(permissions), keyId, userId]
      );

      const success = result.rowCount > 0;
      if (success) {
        logger.info(`API key permissions updated: ${keyId}`);
      }

      return success;
    } catch (error) {
      logger.error('Failed to update API key permissions:', error);
      return false;
    }
  }

  // Rotate an API key (generate new key, keep same permissions)
  public async rotateApiKey(keyId: string, userId: string): Promise<{ apiKey: string; keyData: ApiKeyData } | null> {
    try {
      // Get existing key data
      const existingResult = await this.db.query(
        `SELECT * FROM api_keys WHERE id = $1 AND user_id = $2 AND is_active = true`,
        [keyId, userId]
      );

      if (existingResult.rows.length === 0) {
        return null;
      }

      const existingKey = this.mapDbRowToApiKey(existingResult.rows[0]);

      // Generate new API key
      const newApiKey = this.generateSecureApiKey();
      const newKeyHash = this.hashApiKey(newApiKey);
      const newEncryptedKey = config.encryptApiKey(newApiKey);

      // Update the key
      const result = await this.db.query(
        `
        UPDATE api_keys 
        SET key_hash = $1, encrypted_key = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3 AND user_id = $4
        RETURNING *
        `,
        [newKeyHash, newEncryptedKey, keyId, userId]
      );

      const keyData = this.mapDbRowToApiKey(result.rows[0]);

      logger.info(`API key rotated: ${keyId} for user ${userId}`);

      return { apiKey: newApiKey, keyData };
    } catch (error) {
      logger.error('Failed to rotate API key:', error);
      return null;
    }
  }

  // Private helper methods
  private generateSecureApiKey(): string {
    const prefix = 'uaui_';
    const randomBytes = crypto.randomBytes(32);
    const key = randomBytes.toString('base64url');
    return `${prefix}${key}`;
  }

  private hashApiKey(apiKey: string): string {
    return config.hashApiKey(apiKey);
  }

  private async updateLastUsed(keyId: string): Promise<void> {
    try {
      await this.db.query(
        `UPDATE api_keys SET last_used = CURRENT_TIMESTAMP WHERE id = $1`,
        [keyId]
      );
    } catch (error) {
      logger.error('Failed to update last used timestamp:', error);
    }
  }

  private async deactivateApiKey(keyId: string): Promise<void> {
    try {
      await this.db.query(
        `UPDATE api_keys SET is_active = false WHERE id = $1`,
        [keyId]
      );
      logger.info(`API key deactivated due to expiration: ${keyId}`);
    } catch (error) {
      logger.error('Failed to deactivate expired API key:', error);
    }
  }

  private mapDbRowToApiKey(row: any): ApiKeyData {
    return {
      id: row.id,
      name: row.name,
      keyHash: row.key_hash,
      encryptedKey: row.encrypted_key,
      userId: row.user_id,
      permissions: JSON.parse(row.permissions || '[]'),
      rateLimit: row.rate_limit,
      expiresAt: row.expires_at,
      lastUsed: row.last_used,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  // Cleanup expired keys (run periodically)
  public async cleanupExpiredKeys(): Promise<number> {
    try {
      const result = await this.db.query(
        `
        UPDATE api_keys 
        SET is_active = false 
        WHERE expires_at < CURRENT_TIMESTAMP AND is_active = true
        `
      );

      const count = result.rowCount || 0;
      if (count > 0) {
        logger.info(`Cleaned up ${count} expired API keys`);
      }

      return count;
    } catch (error) {
      logger.error('Failed to cleanup expired API keys:', error);
      return 0;
    }
  }
}
