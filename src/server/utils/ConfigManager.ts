import crypto from "crypto";
import { z } from "zod";
import { Logger } from "./Logger";

const logger = Logger.getInstance();

// Environment validation schema
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  PORT: z.string().transform(Number).default(3001),
  HOST: z.string().default("0.0.0.0"),
  FRONTEND_URL: z.string().url().default("http://localhost:5173"),

  // Database Configuration
  DB_HOST: z.string().default("localhost"),
  DB_PORT: z.string().transform(Number).default(5432),
  DB_NAME: z.string().default("uaui_core"),
  DB_USER: z.string().default("postgres"),
  DB_PASSWORD: z.string().min(1),
  DB_SSL: z.string().transform(val => val === "true").default(false),
  DB_MAX_CONNECTIONS: z.string().transform(Number).default(20),
  DB_IDLE_TIMEOUT: z.string().transform(Number).default(30000),
  DB_CONNECTION_TIMEOUT: z.string().transform(Number).default(2000),

  // Redis Configuration
  REDIS_HOST: z.string().default("localhost"),
  REDIS_PORT: z.string().transform(Number).default(6379),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().transform(Number).default(0),
  REDIS_MAX_RETRIES: z.string().transform(Number).default(3),
  REDIS_RETRY_DELAY: z.string().transform(Number).default(100),

  // Security Configuration
  JWT_SECRET: z.string().min(32),
  ENCRYPTION_KEY: z.string().length(32),
  API_KEY_SALT: z.string().min(16),

  // Rate Limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default(100),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default(60000),
  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: z.string().transform(val => val === "true").default(false),

  // LLM Provider API Keys
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GROQ_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),

  // Tool Service API Keys
  SERPER_API_KEY: z.string().optional(),
  SERPAPI_API_KEY: z.string().optional(),
  OPENWEATHER_API_KEY: z.string().optional(),
  WEATHERAPI_KEY: z.string().optional(),

  // External Service Configuration
  WEB_SEARCH_PROVIDER: z.enum(["serper", "serpapi"]).default("serper"),
  WEATHER_PROVIDER: z.enum(["openweather", "weatherapi"]).default("openweather"),
  SEARCH_RESULTS_LIMIT: z.string().transform(Number).default(10),
  WEATHER_UNITS: z.enum(["metric", "imperial"]).default("metric"),

  // Logging Configuration
  LOG_LEVEL: z.enum(["error", "warn", "info", "debug"]).default("info"),
  LOG_FILE: z.string().default("logs/uaui-core.log"),
  LOG_MAX_SIZE: z.string().default("10m"),
  LOG_MAX_FILES: z.string().transform(Number).default(5),

  // HTTPS Configuration
  HTTPS_ENABLED: z.string().transform(val => val === "true").default(false),
  SSL_CERT_PATH: z.string().optional(),
  SSL_KEY_PATH: z.string().optional(),
  FORCE_HTTPS: z.string().transform(val => val === "true").default(false),

  // CORS Configuration
  CORS_ORIGIN: z.string().default("http://localhost:5173"),
  CORS_CREDENTIALS: z.string().transform(val => val === "true").default(true),
  CORS_METHODS: z.string().default("GET,POST,PUT,DELETE,OPTIONS"),
  CORS_HEADERS: z.string().default("Content-Type,Authorization,X-Requested-With"),

  // Session Configuration
  SESSION_TIMEOUT: z.string().transform(Number).default(3600),
  SESSION_CLEANUP_INTERVAL: z.string().transform(Number).default(300),
  MAX_SESSIONS_PER_USER: z.string().transform(Number).default(10),

  // File Upload Configuration
  MAX_FILE_SIZE: z.string().transform(Number).default(10485760),
  ALLOWED_FILE_TYPES: z.string().default(".txt,.json,.csv,.md"),
  UPLOAD_DIR: z.string().default("uploads"),

  // Monitoring and Health
  HEALTH_CHECK_INTERVAL: z.string().transform(Number).default(30000),
  METRICS_ENABLED: z.string().transform(val => val === "true").default(true),
  PERFORMANCE_MONITORING: z.string().transform(val => val === "true").default(false),

  // Development Tools
  VITE_TEMPO: z.string().transform(val => val === "true").default(false),
  DEBUG_MODE: z.string().transform(val => val === "true").default(false),
  ENABLE_SWAGGER: z.string().transform(val => val === "true").default(false),

  // Feature Flags
  ENABLE_OAUTH: z.string().transform(val => val === "true").default(false),
  ENABLE_WEBSOCKETS: z.string().transform(val => val === "true").default(true),
  ENABLE_STREAMING: z.string().transform(val => val === "true").default(true),
  ENABLE_TOOL_EXECUTION: z.string().transform(val => val === "true").default(true),
  ENABLE_AGENT_MEMORY: z.string().transform(val => val === "true").default(true),
});

export type Config = z.infer<typeof envSchema>;

export class ConfigManager {
  private static instance: ConfigManager;
  private config: Config;
  private encryptionKey: Buffer;

  private constructor() {
    this.validateAndLoadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private validateAndLoadConfig(): void {
    try {
      // Parse and validate environment variables
      const result = envSchema.safeParse(process.env);
      
      if (!result.success) {
        const errors = result.error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join('\n');
        
        logger.error("Environment validation failed:", errors);
        throw new Error(`Environment validation failed:\n${errors}`);
      }

      this.config = result.data;
      this.encryptionKey = Buffer.from(this.config.ENCRYPTION_KEY, 'utf8');

      // Validate critical configurations
      this.validateCriticalConfig();

      logger.info("Configuration loaded and validated successfully");
    } catch (error) {
      logger.error("Failed to load configuration:", error);
      throw error;
    }
  }

  private validateCriticalConfig(): void {
    // Ensure at least one LLM provider is configured
    const llmProviders = [
      this.config.OPENAI_API_KEY,
      this.config.ANTHROPIC_API_KEY,
      this.config.GROQ_API_KEY,
      this.config.OPENROUTER_API_KEY
    ].filter(Boolean);

    if (llmProviders.length === 0) {
      logger.warn("No LLM provider API keys configured. Some features may not work.");
    }

    // Validate encryption key strength
    if (this.config.ENCRYPTION_KEY.length !== 32) {
      throw new Error("ENCRYPTION_KEY must be exactly 32 characters long");
    }

    // Validate JWT secret strength
    if (this.config.JWT_SECRET.length < 32) {
      throw new Error("JWT_SECRET must be at least 32 characters long");
    }

    // Production-specific validations
    if (this.config.NODE_ENV === "production") {
      if (this.config.JWT_SECRET === "your_super_secure_jwt_secret_key_here_minimum_32_characters") {
        throw new Error("Default JWT_SECRET detected in production. Please set a secure secret.");
      }
      
      if (this.config.ENCRYPTION_KEY === "your_32_character_encryption_key_here") {
        throw new Error("Default ENCRYPTION_KEY detected in production. Please set a secure key.");
      }
    }
  }

  public get<K extends keyof Config>(key: K): Config[K] {
    return this.config[key];
  }

  public getAll(): Config {
    return { ...this.config };
  }

  public isDevelopment(): boolean {
    return this.config.NODE_ENV === "development";
  }

  public isProduction(): boolean {
    return this.config.NODE_ENV === "production";
  }

  public isTest(): boolean {
    return this.config.NODE_ENV === "test";
  }

  // Secure API key encryption/decryption
  public encryptApiKey(apiKey: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
      
      let encrypted = cipher.update(apiKey, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      logger.error("Failed to encrypt API key:", error);
      throw new Error("API key encryption failed");
    }
  }

  public decryptApiKey(encryptedApiKey: string): string {
    try {
      const parts = encryptedApiKey.split(':');
      if (parts.length !== 2) {
        throw new Error("Invalid encrypted API key format");
      }

      const iv = Buffer.from(parts[0], 'hex');
      const encryptedData = parts[1];
      
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
      
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error("Failed to decrypt API key:", error);
      throw new Error("API key decryption failed");
    }
  }

  // Hash API key for storage (one-way)
  public hashApiKey(apiKey: string): string {
    return crypto
      .createHash('sha256')
      .update(apiKey + this.config.API_KEY_SALT)
      .digest('hex');
  }

  // Validate required API keys for specific providers
  public validateProviderConfig(provider: string): boolean {
    switch (provider.toLowerCase()) {
      case 'openai':
        return !!this.config.OPENAI_API_KEY;
      case 'claude':
      case 'anthropic':
        return !!this.config.ANTHROPIC_API_KEY;
      case 'groq':
        return !!this.config.GROQ_API_KEY;
      case 'openrouter':
        return !!this.config.OPENROUTER_API_KEY;
      default:
        return false;
    }
  }

  // Get provider API key
  public getProviderApiKey(provider: string): string | null {
    switch (provider.toLowerCase()) {
      case 'openai':
        return this.config.OPENAI_API_KEY || null;
      case 'claude':
      case 'anthropic':
        return this.config.ANTHROPIC_API_KEY || null;
      case 'groq':
        return this.config.GROQ_API_KEY || null;
      case 'openrouter':
        return this.config.OPENROUTER_API_KEY || null;
      default:
        return null;
    }
  }

  // Get tool service API key
  public getToolApiKey(service: string): string | null {
    switch (service.toLowerCase()) {
      case 'serper':
        return this.config.SERPER_API_KEY || null;
      case 'serpapi':
        return this.config.SERPAPI_API_KEY || null;
      case 'openweather':
        return this.config.OPENWEATHER_API_KEY || null;
      case 'weatherapi':
        return this.config.WEATHERAPI_KEY || null;
      default:
        return null;
    }
  }
}
