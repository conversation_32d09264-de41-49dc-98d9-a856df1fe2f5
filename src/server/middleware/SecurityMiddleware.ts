import { FastifyRequest, FastifyReply } from "fastify";
import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export class SecurityMiddleware {
  // Input sanitization for XSS protection
  static sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return input
        .replace(/[<>]/g, '') // Remove HTML brackets
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .replace(/script/gi, '') // Remove script tags
        .trim();
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item));
    }
    
    if (input && typeof input === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[this.sanitizeInput(key)] = this.sanitizeInput(value);
      }
      return sanitized;
    }
    
    return input;
  }

  // Validate and sanitize request body
  static async sanitizeRequestBody(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    try {
      if (request.body) {
        request.body = this.sanitizeInput(request.body);
      }
      
      if (request.query) {
        request.query = this.sanitizeInput(request.query);
      }
      
      done();
    } catch (error) {
      logger.error('Input sanitization error:', error);
      reply.code(400).send({
        success: false,
        error: 'Invalid input data',
      });
    }
  }

  // Security headers middleware
  static async addSecurityHeaders(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    // Content Security Policy
    reply.header('Content-Security-Policy', [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self' ws: wss:",
      "frame-ancestors 'none'",
    ].join('; '));

    // Prevent XSS attacks
    reply.header('X-XSS-Protection', '1; mode=block');
    
    // Prevent MIME type sniffing
    reply.header('X-Content-Type-Options', 'nosniff');
    
    // Prevent clickjacking
    reply.header('X-Frame-Options', 'DENY');
    
    // Strict Transport Security (HTTPS only)
    if (config.get('HTTPS_ENABLED')) {
      reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
    
    // Referrer Policy
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Permissions Policy
    reply.header('Permissions-Policy', [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
    ].join(', '));

    done();
  }

  // Rate limiting per user/IP
  static createRateLimitKeyGenerator() {
    return (request: FastifyRequest): string => {
      // Use user ID if authenticated, otherwise use IP
      const userId = (request as any).user?.id;
      if (userId) {
        return `user:${userId}`;
      }
      
      // Get real IP address considering proxies
      const forwarded = request.headers['x-forwarded-for'];
      const ip = forwarded 
        ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0])
        : request.ip;
      
      return `ip:${ip}`;
    };
  }

  // HTTPS redirect middleware
  static async enforceHTTPS(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    if (config.get('FORCE_HTTPS') && !request.headers['x-forwarded-proto']?.includes('https')) {
      const httpsUrl = `https://${request.headers.host}${request.url}`;
      reply.code(301).redirect(httpsUrl);
      return;
    }
    done();
  }

  // API key validation middleware
  static async validateApiKey(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    const apiKey = request.headers['x-api-key'] as string;
    
    if (!apiKey) {
      reply.code(401).send({
        success: false,
        error: 'API key required',
      });
      return;
    }

    // Validate API key format
    if (!this.isValidApiKeyFormat(apiKey)) {
      reply.code(401).send({
        success: false,
        error: 'Invalid API key format',
      });
      return;
    }

    // TODO: Implement actual API key validation against database
    // For now, just check if it's not empty
    done();
  }

  // JWT token validation middleware
  static async validateJWT(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    try {
      const token = request.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        reply.code(401).send({
          success: false,
          error: 'Authorization token required',
        });
        return;
      }

      // Verify JWT token
      const decoded = request.server.jwt.verify(token);
      (request as any).user = decoded;
      
      done();
    } catch (error) {
      logger.warn('JWT validation failed:', error.message);
      reply.code(401).send({
        success: false,
        error: 'Invalid or expired token',
      });
    }
  }

  // Request size validation
  static async validateRequestSize(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    const maxSize = config.get('MAX_FILE_SIZE');
    const contentLength = parseInt(request.headers['content-length'] || '0');
    
    if (contentLength > maxSize) {
      reply.code(413).send({
        success: false,
        error: 'Request entity too large',
      });
      return;
    }
    
    done();
  }

  // Content type validation
  static async validateContentType(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    const allowedTypes = ['application/json', 'multipart/form-data', 'application/x-www-form-urlencoded'];
    const contentType = request.headers['content-type'];
    
    if (request.method !== 'GET' && contentType) {
      const isAllowed = allowedTypes.some(type => contentType.includes(type));
      if (!isAllowed) {
        reply.code(415).send({
          success: false,
          error: 'Unsupported media type',
        });
        return;
      }
    }
    
    done();
  }

  // Helper methods
  private static isValidApiKeyFormat(apiKey: string): boolean {
    // Basic API key format validation
    return /^[a-zA-Z0-9_-]{20,}$/.test(apiKey);
  }

  // SQL injection prevention (for raw queries)
  static sanitizeSQLInput(input: string): string {
    return input
      .replace(/[';--]/g, '') // Remove SQL comment and statement terminators
      .replace(/\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE)\b/gi, '') // Remove dangerous SQL keywords
      .trim();
  }

  // NoSQL injection prevention
  static sanitizeNoSQLInput(input: any): any {
    if (typeof input === 'string') {
      return input.replace(/[${}]/g, ''); // Remove MongoDB operators
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeNoSQLInput(item));
    }
    
    if (input && typeof input === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        // Skip keys that start with $ (MongoDB operators)
        if (!key.startsWith('$')) {
          sanitized[key] = this.sanitizeNoSQLInput(value);
        }
      }
      return sanitized;
    }
    
    return input;
  }

  // CSRF token validation (for future implementation)
  static async validateCSRFToken(
    request: FastifyRequest,
    reply: FastifyReply,
    done: Function
  ): Promise<void> {
    // TODO: Implement CSRF token validation
    done();
  }
}
