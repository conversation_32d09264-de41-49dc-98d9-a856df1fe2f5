import { EventBus } from "../core/EventBus";
import { StateManager } from "../core/StateManager";
import { ProviderService } from "../services/ProviderService";
import { APIXEvent } from "../types/apix";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class ProviderHandler {
  private providerService: ProviderService;
  private eventBus: EventBus;
  private stateManager: StateManager;

  constructor(eventBus: EventBus, stateManager: StateManager) {
    this.eventBus = eventBus;
    this.stateManager = stateManager;
    this.providerService = new ProviderService(stateManager, eventBus);
  }

  async configure(event: APIXEvent): Promise<any> {
    try {
      logger.info("Configuring provider", {
        eventId: event.id,
        providerName: event.data.name,
      });

      const provider = await this.providerService.configure(event.data.name, {
        apiKey: event.data.apiKey,
        enabled: event.data.enabled,
        priority: event.data.priority,
        fallbackEnabled: event.data.fallbackEnabled,
        maxRetries: event.data.maxRetries,
      });

      return provider;
    } catch (error) {
      logger.error("Failed to configure provider:", error);
      throw error;
    }
  }

  async status(event: APIXEvent): Promise<any> {
    try {
      logger.info("Getting provider status", {
        eventId: event.id,
        providerName: event.data.name,
      });

      const status = await this.providerService.getStatus(event.data.name);

      return status;
    } catch (error) {
      logger.error("Failed to get provider status:", error);
      throw error;
    }
  }

  async test(event: APIXEvent): Promise<any> {
    try {
      logger.info("Testing provider", {
        eventId: event.id,
        providerName: event.data.name,
      });

      const result = await this.providerService.test(event.data.name);

      return result;
    } catch (error) {
      logger.error("Failed to test provider:", error);
      throw error;
    }
  }

  async list(event: APIXEvent): Promise<any> {
    try {
      logger.info("Listing providers", { eventId: event.id });

      const providers = await this.providerService.list();

      return { providers };
    } catch (error) {
      logger.error("Failed to list providers:", error);
      throw error;
    }
  }
}
