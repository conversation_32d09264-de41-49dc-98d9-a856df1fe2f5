import { EventBus } from "../core/EventBus";
import { StateManager } from "../core/StateManager";
import { AgentService } from "../services/AgentService";
import { APIXEvent } from "../types/apix";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class WebSocketHandler {
  private agentService: AgentService;
  private eventBus: EventBus;
  private stateManager: StateManager;

  constructor(eventBus: EventBus, stateManager: StateManager) {
    this.eventBus = eventBus;
    this.stateManager = stateManager;
    this.agentService = new AgentService(stateManager, eventBus);
  }

  async agentSwitch(event: APIXEvent): Promise<any> {
    try {
      logger.info("Switching agent", {
        eventId: event.id,
        agentId: event.data.agentId,
        sessionId: event.data.sessionId,
      });

      // Validate agent exists
      const agent = await this.agentService.read(event.data.agentId);
      if (!agent) {
        throw new Error("Agent not found");
      }

      // Update session state
      const sessionKey = `session:${event.data.sessionId}`;
      await this.stateManager.setHash(sessionKey, "currentAgent", {
        agentId: event.data.agentId,
        switchedAt: new Date().toISOString(),
        userId: event.metadata?.userId,
      });

      // Emit system event
      this.eventBus.emitSystemEvent("agent_switched", {
        agentId: event.data.agentId,
        sessionId: event.data.sessionId,
        userId: event.metadata?.userId,
      });

      return {
        success: true,
        agent,
        sessionId: event.data.sessionId,
        switchedAt: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Failed to switch agent:", error);
      throw error;
    }
  }

  async systemStatus(event: APIXEvent): Promise<any> {
    try {
      logger.info("Getting system status", { eventId: event.id });

      // Get various system metrics
      const redisStatus = await this.stateManager.getStatus();
      const agentStats = this.agentService.getStats();

      const status = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        services: {
          redis: redisStatus.connected ? "connected" : "disconnected",
          database: "connected", // This would be checked from Database service
          websocket: "connected",
          http: "connected",
        },
        metrics: {
          connectedClients: 0, // This would come from APIXIntegrationLayer
          activeAgents: agentStats.totalRuntime,
          registeredTools: 0, // This would come from ToolService
          enabledProviders: 0, // This would come from ProviderService
        },
        redis: redisStatus,
        agents: agentStats,
      };

      return status;
    } catch (error) {
      logger.error("Failed to get system status:", error);
      throw error;
    }
  }
}
