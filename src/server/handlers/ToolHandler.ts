import { EventBus } from "../core/EventBus";
import { StateManager } from "../core/StateManager";
import { ToolService } from "../services/ToolService";
import { APIXEvent } from "../types/apix";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class ToolHandler {
  private toolService: ToolService;
  private eventBus: EventBus;
  private stateManager: StateManager;

  constructor(eventBus: EventBus, stateManager: StateManager) {
    this.eventBus = eventBus;
    this.stateManager = stateManager;
    this.toolService = new ToolService(stateManager, eventBus);
  }

  async execute(event: APIXEvent): Promise<any> {
    try {
      logger.info("Executing tool", {
        eventId: event.id,
        toolName: event.data.toolName,
      });

      const result = await this.toolService.execute(
        event.data.toolName,
        event.data.parameters,
        {
          agentId: event.data.agentId,
          userId: event.metadata?.userId,
          sessionId: event.metadata?.sessionId,
        },
      );

      return result;
    } catch (error) {
      logger.error("Failed to execute tool:", error);
      throw error;
    }
  }

  async list(event: APIXEvent): Promise<any> {
    try {
      logger.info("Listing tools", { eventId: event.id });

      const { type, active } = event.data || {};

      const tools = await this.toolService.list({ type, active });

      return { tools };
    } catch (error) {
      logger.error("Failed to list tools:", error);
      throw error;
    }
  }

  async create(event: APIXEvent): Promise<any> {
    try {
      logger.info("Creating tool", { eventId: event.id });

      const tool = await this.toolService.create(event.data);

      return tool;
    } catch (error) {
      logger.error("Failed to create tool:", error);
      throw error;
    }
  }

  async update(event: APIXEvent): Promise<any> {
    try {
      logger.info("Updating tool", {
        eventId: event.id,
        toolId: event.data.id,
      });

      const { id, ...updateData } = event.data;
      const tool = await this.toolService.update(id, updateData);

      if (!tool) {
        throw new Error("Tool not found");
      }

      return tool;
    } catch (error) {
      logger.error("Failed to update tool:", error);
      throw error;
    }
  }

  async delete(event: APIXEvent): Promise<any> {
    try {
      logger.info("Deleting tool", {
        eventId: event.id,
        toolId: event.data.id,
      });

      const deleted = await this.toolService.delete(event.data.id);

      if (!deleted) {
        throw new Error("Tool not found");
      }

      return { success: true, message: "Tool deleted successfully" };
    } catch (error) {
      logger.error("Failed to delete tool:", error);
      throw error;
    }
  }
}
