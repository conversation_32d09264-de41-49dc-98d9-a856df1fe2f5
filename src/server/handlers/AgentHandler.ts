import { EventBus } from "../core/EventBus";
import { StateManager } from "../core/StateManager";
import { AgentService } from "../services/AgentService";
import { APIXEvent, APIXResponse } from "../types/apix";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class AgentHandler {
  private agentService: AgentService;
  private eventBus: EventBus;
  private stateManager: StateManager;

  constructor(eventBus: EventBus, stateManager: StateManager) {
    this.eventBus = eventBus;
    this.stateManager = stateManager;
    this.agentService = new AgentService(stateManager, eventBus);
  }

  async create(event: APIXEvent): Promise<any> {
    try {
      logger.info("Creating agent", { eventId: event.id });

      const agent = await this.agentService.create({
        ...event.data,
        createdBy: event.metadata?.userId,
      });

      return agent;
    } catch (error) {
      logger.error("Failed to create agent:", error);
      throw error;
    }
  }

  async read(event: APIXEvent): Promise<any> {
    try {
      logger.info("Reading agent", {
        eventId: event.id,
        agentId: event.data.id,
      });

      const agent = await this.agentService.read(event.data.id);

      if (!agent) {
        throw new Error("Agent not found");
      }

      return agent;
    } catch (error) {
      logger.error("Failed to read agent:", error);
      throw error;
    }
  }

  async update(event: APIXEvent): Promise<any> {
    try {
      logger.info("Updating agent", {
        eventId: event.id,
        agentId: event.data.id,
      });

      const { id, ...updateData } = event.data;
      const agent = await this.agentService.update(id, updateData);

      if (!agent) {
        throw new Error("Agent not found");
      }

      return agent;
    } catch (error) {
      logger.error("Failed to update agent:", error);
      throw error;
    }
  }

  async delete(event: APIXEvent): Promise<any> {
    try {
      logger.info("Deleting agent", {
        eventId: event.id,
        agentId: event.data.id,
      });

      const deleted = await this.agentService.delete(event.data.id);

      if (!deleted) {
        throw new Error("Agent not found");
      }

      return { success: true, message: "Agent deleted successfully" };
    } catch (error) {
      logger.error("Failed to delete agent:", error);
      throw error;
    }
  }

  async list(event: APIXEvent): Promise<any> {
    try {
      logger.info("Listing agents", { eventId: event.id });

      const { limit = 50, offset = 0, filter = {} } = event.data || {};

      const agents = await this.agentService.list(
        {
          ...filter,
          createdBy: event.metadata?.userId,
        },
        limit,
        offset,
      );

      return {
        agents,
        pagination: {
          limit,
          offset,
          total: agents.length,
        },
      };
    } catch (error) {
      logger.error("Failed to list agents:", error);
      throw error;
    }
  }

  async execute(event: APIXEvent): Promise<any> {
    try {
      logger.info("Executing agent", {
        eventId: event.id,
        agentId: event.data.agentId,
        sessionId: event.data.sessionId,
      });

      const result = await this.agentService.execute(
        event.data.agentId,
        event.data.input,
        event.data.sessionId,
        event.data.context,
        event.data.streaming || false,
      );

      return result;
    } catch (error) {
      logger.error("Failed to execute agent:", error);
      throw error;
    }
  }
}
