import { Database } from "../database/Database";
import { StateManager } from "../core/StateManager";
import { EventBus } from "../core/EventBus";
import { Logger } from "../utils/Logger";
import { z } from "zod";

const logger = Logger.getInstance();

export interface Tool {
  id: string;
  name: string;
  description: string;
  type: "internal" | "external";
  schemaDefinition: Record<string, any>;
  endpointUrl?: string;
  authentication?: Record<string, any>;
  configuration?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  version: number;
}

export interface CreateToolData {
  name: string;
  description: string;
  type: "internal" | "external";
  schema: string;
  endpoint?: string;
  active: boolean;
}

export interface UpdateToolData {
  name?: string;
  description?: string;
  type?: "internal" | "external";
  schema?: string;
  endpoint?: string;
  active?: boolean;
}

export interface ToolFilter {
  type?: "internal" | "external";
  active?: boolean;
}

export interface ExecutionContext {
  agentId?: string;
  userId?: string;
  sessionId?: string;
}

export class ToolService {
  private db: Database;
  private stateManager: StateManager;
  private eventBus: EventBus;
  private runtimeTools: Map<string, Tool> = new Map();

  constructor(stateManager: StateManager, eventBus: EventBus) {
    this.db = Database.getInstance();
    this.stateManager = stateManager;
    this.eventBus = eventBus;
    this.loadActiveTools();
  }

  async create(data: CreateToolData): Promise<Tool> {
    try {
      // Validate schema
      let schemaDefinition: Record<string, any>;
      try {
        schemaDefinition = JSON.parse(data.schema);
      } catch (error) {
        throw new Error("Invalid JSON schema");
      }

      const result = await this.db.query(
        `
        INSERT INTO tools (name, description, type, schema_definition, endpoint_url, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `,
        [
          data.name,
          data.description,
          data.type,
          JSON.stringify(schemaDefinition),
          data.endpoint,
          data.active,
        ],
      );

      const tool = this.mapDbRowToTool(result.rows[0]);

      // Load into runtime if active
      if (tool.isActive) {
        this.runtimeTools.set(tool.name, tool);
      }

      // Cache in Redis
      await this.stateManager.set(`tool:${tool.id}`, tool);

      // Emit event
      this.eventBus.emitToolEvent("created", tool.name, tool);

      logger.info(`Tool created: ${tool.name} (${tool.id})`);
      return tool;
    } catch (error) {
      logger.error("Failed to create tool:", error);
      throw error;
    }
  }

  async read(id: string): Promise<Tool | null> {
    try {
      // Check Redis cache
      const cached = await this.stateManager.get(`tool:${id}`);
      if (cached) {
        return cached;
      }

      // Query database
      const result = await this.db.query(
        "SELECT * FROM tools WHERE id = $1 AND is_active = true",
        [id],
      );

      if (result.rows.length === 0) {
        return null;
      }

      const tool = this.mapDbRowToTool(result.rows[0]);

      // Cache in Redis
      await this.stateManager.set(`tool:${id}`, tool);

      return tool;
    } catch (error) {
      logger.error(`Failed to read tool ${id}:`, error);
      throw error;
    }
  }

  async update(id: string, data: UpdateToolData): Promise<Tool | null> {
    try {
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (data.name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(data.name);
      }
      if (data.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        updateValues.push(data.description);
      }
      if (data.type !== undefined) {
        updateFields.push(`type = $${paramIndex++}`);
        updateValues.push(data.type);
      }
      if (data.schema !== undefined) {
        let schemaDefinition: Record<string, any>;
        try {
          schemaDefinition = JSON.parse(data.schema);
        } catch (error) {
          throw new Error("Invalid JSON schema");
        }
        updateFields.push(`schema_definition = $${paramIndex++}`);
        updateValues.push(JSON.stringify(schemaDefinition));
      }
      if (data.endpoint !== undefined) {
        updateFields.push(`endpoint_url = $${paramIndex++}`);
        updateValues.push(data.endpoint);
      }
      if (data.active !== undefined) {
        updateFields.push(`is_active = $${paramIndex++}`);
        updateValues.push(data.active);
      }

      updateFields.push(`version = version + 1`);
      updateValues.push(id);

      const result = await this.db.query(
        `
        UPDATE tools 
        SET ${updateFields.join(", ")}
        WHERE id = $${paramIndex} AND is_active = true
        RETURNING *
      `,
        updateValues,
      );

      if (result.rows.length === 0) {
        return null;
      }

      const tool = this.mapDbRowToTool(result.rows[0]);

      // Update runtime cache
      if (tool.isActive) {
        this.runtimeTools.set(tool.name, tool);
      } else {
        this.runtimeTools.delete(tool.name);
      }

      // Update Redis cache
      await this.stateManager.set(`tool:${id}`, tool);

      // Emit event
      this.eventBus.emitToolEvent("updated", tool.name, tool);

      logger.info(`Tool updated: ${tool.name} (${id})`);
      return tool;
    } catch (error) {
      logger.error(`Failed to update tool ${id}:`, error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        `
        UPDATE tools 
        SET is_active = false
        WHERE id = $1 AND is_active = true
        RETURNING name
      `,
        [id],
      );

      if (result.rows.length === 0) {
        return false;
      }

      const toolName = result.rows[0].name;

      // Remove from runtime cache
      this.runtimeTools.delete(toolName);

      // Remove from Redis cache
      await this.stateManager.delete(`tool:${id}`);

      // Emit event
      this.eventBus.emitToolEvent("deleted", toolName, { id });

      logger.info(`Tool deleted: ${toolName} (${id})`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete tool ${id}:`, error);
      throw error;
    }
  }

  async list(filter?: ToolFilter): Promise<Tool[]> {
    try {
      let whereClause = "WHERE is_active = true";
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (filter?.type) {
        whereClause += ` AND type = $${paramIndex++}`;
        queryParams.push(filter.type);
      }

      const result = await this.db.query(
        `
        SELECT * FROM tools
        ${whereClause}
        ORDER BY updated_at DESC
      `,
        queryParams,
      );

      return result.rows.map((row) => this.mapDbRowToTool(row));
    } catch (error) {
      logger.error("Failed to list tools:", error);
      throw error;
    }
  }

  async execute(
    toolName: string,
    parameters: Record<string, any>,
    context: ExecutionContext,
  ): Promise<any> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      const tool = this.runtimeTools.get(toolName);
      if (!tool) {
        throw new Error(`Tool not found: ${toolName}`);
      }

      // Emit tool call start event
      this.eventBus.emit("tool:call_start", {
        toolId: tool.id,
        toolName,
        parameters,
        agentId: context.agentId,
        sessionId: context.sessionId,
        executionId,
      });

      // Validate parameters against schema
      this.validateParameters(tool, parameters);

      const startTime = Date.now();
      let result: any;
      let status: "success" | "failed" | "timeout" | "cancelled" = "success";
      let errorMessage: string | null = null;
      let retryCount = 0;
      const maxRetries = tool.configuration?.maxRetries || 3;

      while (retryCount <= maxRetries) {
        try {
          if (tool.type === "internal") {
            result = await this.executeInternalTool(tool, parameters);
          } else {
            result = await this.executeExternalTool(tool, parameters);
          }
          break; // Success, exit retry loop
        } catch (error) {
          retryCount++;
          errorMessage = error.message;

          if (retryCount <= maxRetries) {
            // Emit retry event
            this.eventBus.emit("tool:call_error", {
              toolId: tool.id,
              toolName,
              executionId,
              error: errorMessage,
              retryCount,
              willRetry: true,
            });

            // Wait before retry with exponential backoff
            await new Promise((resolve) =>
              setTimeout(resolve, Math.pow(2, retryCount) * 1000),
            );
          } else {
            status = "failed";
            throw error;
          }
        }
      }

      const executionTime = Date.now() - startTime;

      // Emit tool call result event
      this.eventBus.emit("tool:call_result", {
        toolId: tool.id,
        toolName,
        executionId,
        result,
        executionTime,
        success: true,
      });

      // Log execution
      await this.logExecution({
        toolId: tool.id,
        agentId: context.agentId,
        userId: context.userId,
        sessionId: context.sessionId,
        inputParameters: parameters,
        outputResult: result,
        executionTime,
        status,
        errorMessage,
      });

      // Emit legacy event for backward compatibility
      this.eventBus.emitToolEvent("executed", toolName, {
        tool,
        parameters,
        result,
        context,
        executionId,
      });

      return result;
    } catch (error) {
      // Emit final error event
      this.eventBus.emit("tool:call_error", {
        toolId: toolName,
        toolName,
        executionId,
        error: error.message,
        retryCount: 0,
        willRetry: false,
      });

      logger.error(`Failed to execute tool ${toolName}:`, error);
      throw error;
    }
  }

  private validateParameters(
    tool: Tool,
    parameters: Record<string, any>,
  ): void {
    // Basic validation - in production, use a proper schema validator
    const schema = tool.schemaDefinition;
    if (schema.input) {
      for (const [key, type] of Object.entries(schema.input)) {
        if (!(key in parameters)) {
          throw new Error(`Missing required parameter: ${key}`);
        }
        // Add type checking here
      }
    }
  }

  private async executeInternalTool(
    tool: Tool,
    parameters: Record<string, any>,
  ): Promise<any> {
    // Execute internal tools based on their name
    switch (tool.name) {
      case "calculator":
        return this.executeCalculator(parameters);
      default:
        throw new Error(`Unknown internal tool: ${tool.name}`);
    }
  }

  private async executeExternalTool(
    tool: Tool,
    parameters: Record<string, any>,
  ): Promise<any> {
    if (!tool.endpointUrl) {
      throw new Error("External tool missing endpoint URL");
    }

    // Make HTTP request to external tool
    const response = await fetch(tool.endpointUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...this.getAuthHeaders(tool),
      },
      body: JSON.stringify(parameters),
    });

    if (!response.ok) {
      throw new Error(`External tool request failed: ${response.statusText}`);
    }

    return await response.json();
  }

  private executeCalculator(parameters: { expression: string }): any {
    try {
      // Simple calculator - in production, use a safe math evaluator
      const result = eval(parameters.expression);
      return { result };
    } catch (error) {
      throw new Error(`Calculator error: ${error.message}`);
    }
  }

  private getAuthHeaders(tool: Tool): Record<string, string> {
    const headers: Record<string, string> = {};

    if (tool.authentication) {
      // Add authentication headers based on tool configuration
      if (tool.authentication.type === "bearer") {
        headers["Authorization"] = `Bearer ${tool.authentication.token}`;
      } else if (tool.authentication.type === "apikey") {
        headers[tool.authentication.header || "X-API-Key"] =
          tool.authentication.key;
      }
    }

    return headers;
  }

  private async logExecution(data: {
    toolId: string;
    agentId?: string;
    userId?: string;
    sessionId?: string;
    inputParameters: Record<string, any>;
    outputResult?: any;
    executionTime: number;
    status: string;
    errorMessage?: string | null;
  }): Promise<void> {
    try {
      await this.db.query(
        `
        INSERT INTO tool_executions 
        (tool_id, agent_id, user_id, session_id, input_parameters, output_result, execution_time_ms, status, error_message)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `,
        [
          data.toolId,
          data.agentId,
          data.userId,
          data.sessionId,
          JSON.stringify(data.inputParameters),
          data.outputResult ? JSON.stringify(data.outputResult) : null,
          data.executionTime,
          data.status,
          data.errorMessage,
        ],
      );
    } catch (error) {
      logger.error("Failed to log tool execution:", error);
    }
  }

  async loadActiveTools(): Promise<void> {
    try {
      const result = await this.db.query(
        "SELECT * FROM tools WHERE is_active = true",
      );

      for (const row of result.rows) {
        const tool = this.mapDbRowToTool(row);
        this.runtimeTools.set(tool.name, tool);
        await this.stateManager.set(`tool:${tool.id}`, tool);
      }

      logger.info(`Loaded ${result.rows.length} active tools into runtime`);
    } catch (error) {
      logger.error("Failed to load active tools:", error);
    }
  }

  private mapDbRowToTool(row: any): Tool {
    return {
      id: row.id,
      name: row.name,
      description: row.description || "",
      type: row.type,
      schemaDefinition: row.schema_definition || {},
      endpointUrl: row.endpoint_url,
      authentication: row.authentication || {},
      configuration: row.configuration || {},
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString(),
      isActive: row.is_active,
      version: row.version,
    };
  }

  getStats(): any {
    return {
      totalRuntime: this.runtimeTools.size,
      runtimeTools: Array.from(this.runtimeTools.keys()),
    };
  }
}
