import { Database } from "../database/Database";
import { StateManager } from "../core/StateManager";
import { EventBus } from "../core/EventBus";
import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";
import { ProviderFactory } from "../providers/ProviderFactory";
import { LLMMessage } from "../providers/BaseLLMProvider";
import { AgentResponse } from "../types/apix";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface Agent {
  id: string;
  name: string;
  description: string;
  provider: string;
  hasMemory: boolean;
  tools: string[];
  promptTemplate: string;
  configuration?: Record<string, any>;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  version: number;
  // Enhanced properties for SynapseAI
  memoryConfig?: {
    ttl?: number;
    maxTokens?: number;
    persistentMemory?: boolean;
    contextWindow?: number;
  };
  sessionConfig?: {
    maxSessions?: number;
    sessionTtl?: number;
    allowConcurrent?: boolean;
  };
  streamingConfig?: {
    enabled?: boolean;
    chunkSize?: number;
    bufferTimeout?: number;
  };
}

export interface CreateAgentData {
  name: string;
  description: string;
  provider: string;
  hasMemory: boolean;
  tools: string[];
  promptTemplate: string;
  configuration?: Record<string, any>;
  createdBy?: string;
}

export interface UpdateAgentData {
  name?: string;
  description?: string;
  provider?: string;
  hasMemory?: boolean;
  tools?: string[];
  promptTemplate?: string;
  configuration?: Record<string, any>;
}

export interface AgentFilter {
  provider?: string;
  hasMemory?: boolean;
  isActive?: boolean;
  createdBy?: string;
}

export class AgentService {
  private db: Database;
  private stateManager: StateManager;
  private eventBus: EventBus;
  private providerFactory: ProviderFactory;
  private runtimeAgents: Map<string, Agent> = new Map();

  constructor(stateManager: StateManager, eventBus: EventBus) {
    this.db = Database.getInstance();
    this.stateManager = stateManager;
    this.eventBus = eventBus;
    this.providerFactory = ProviderFactory.getInstance();
    this.loadActiveAgents();
  }

  async create(data: CreateAgentData): Promise<Agent> {
    try {
      const result = await this.db.query(
        `
        INSERT INTO agents (name, description, provider, has_memory, prompt_template, configuration, created_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `,
        [
          data.name,
          data.description,
          data.provider,
          data.hasMemory,
          data.promptTemplate,
          JSON.stringify(data.configuration || {}),
          data.createdBy,
        ],
      );

      const agent = this.mapDbRowToAgent(result.rows[0]);

      // Add tools if provided
      if (data.tools && data.tools.length > 0) {
        await this.updateAgentTools(agent.id, data.tools);
        agent.tools = data.tools;
      }

      // Load into runtime
      this.runtimeAgents.set(agent.id, agent);

      // Cache in Redis
      await this.stateManager.set(`agent:${agent.id}`, agent);

      // Emit event
      this.eventBus.emitAgentEvent("created", agent.id, agent);

      logger.info(`Agent created: ${agent.name} (${agent.id})`);
      return agent;
    } catch (error) {
      logger.error("Failed to create agent:", error);
      throw error;
    }
  }

  async read(id: string): Promise<Agent | null> {
    try {
      // Check runtime cache first
      if (this.runtimeAgents.has(id)) {
        return this.runtimeAgents.get(id)!;
      }

      // Check Redis cache
      const cached = await this.stateManager.get(`agent:${id}`);
      if (cached) {
        this.runtimeAgents.set(id, cached);
        return cached;
      }

      // Query database
      const result = await this.db.query(
        `
        SELECT a.*, array_agg(t.name) as tool_names
        FROM agents a
        LEFT JOIN agent_tools at ON a.id = at.agent_id
        LEFT JOIN tools t ON at.tool_id = t.id
        WHERE a.id = $1 AND a.is_active = true
        GROUP BY a.id
      `,
        [id],
      );

      if (result.rows.length === 0) {
        return null;
      }

      const agent = this.mapDbRowToAgent(result.rows[0]);

      // Cache in runtime and Redis
      this.runtimeAgents.set(id, agent);
      await this.stateManager.set(`agent:${id}`, agent);

      return agent;
    } catch (error) {
      logger.error(`Failed to read agent ${id}:`, error);
      throw error;
    }
  }

  async update(id: string, data: UpdateAgentData): Promise<Agent | null> {
    try {
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (data.name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(data.name);
      }
      if (data.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        updateValues.push(data.description);
      }
      if (data.provider !== undefined) {
        updateFields.push(`provider = $${paramIndex++}`);
        updateValues.push(data.provider);
      }
      if (data.hasMemory !== undefined) {
        updateFields.push(`has_memory = $${paramIndex++}`);
        updateValues.push(data.hasMemory);
      }
      if (data.promptTemplate !== undefined) {
        updateFields.push(`prompt_template = $${paramIndex++}`);
        updateValues.push(data.promptTemplate);
      }
      if (data.configuration !== undefined) {
        updateFields.push(`configuration = $${paramIndex++}`);
        updateValues.push(JSON.stringify(data.configuration));
      }

      updateFields.push(`version = version + 1`);
      updateValues.push(id);

      const result = await this.db.query(
        `
        UPDATE agents 
        SET ${updateFields.join(", ")}
        WHERE id = $${paramIndex} AND is_active = true
        RETURNING *
      `,
        updateValues,
      );

      if (result.rows.length === 0) {
        return null;
      }

      // Update tools if provided
      if (data.tools !== undefined) {
        await this.updateAgentTools(id, data.tools);
      }

      const agent = await this.read(id); // Reload with tools
      if (!agent) return null;

      // Update caches
      this.runtimeAgents.set(id, agent);
      await this.stateManager.set(`agent:${id}`, agent);

      // Emit event
      this.eventBus.emitAgentEvent("updated", id, agent);

      logger.info(`Agent updated: ${agent.name} (${id})`);
      return agent;
    } catch (error) {
      logger.error(`Failed to update agent ${id}:`, error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        `
        UPDATE agents 
        SET is_active = false
        WHERE id = $1 AND is_active = true
        RETURNING id
      `,
        [id],
      );

      if (result.rows.length === 0) {
        return false;
      }

      // Remove from caches
      this.runtimeAgents.delete(id);
      await this.stateManager.delete(`agent:${id}`);

      // Emit event
      this.eventBus.emitAgentEvent("deleted", id, { id });

      logger.info(`Agent deleted: ${id}`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete agent ${id}:`, error);
      throw error;
    }
  }

  async list(
    filter?: AgentFilter,
    limit: number = 50,
    offset: number = 0,
  ): Promise<Agent[]> {
    try {
      let whereClause = "WHERE a.is_active = true";
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (filter?.provider) {
        whereClause += ` AND a.provider = $${paramIndex++}`;
        queryParams.push(filter.provider);
      }
      if (filter?.hasMemory !== undefined) {
        whereClause += ` AND a.has_memory = $${paramIndex++}`;
        queryParams.push(filter.hasMemory);
      }
      if (filter?.createdBy) {
        whereClause += ` AND a.created_by = $${paramIndex++}`;
        queryParams.push(filter.createdBy);
      }

      queryParams.push(limit, offset);

      const result = await this.db.query(
        `
        SELECT a.*, array_agg(t.name) as tool_names
        FROM agents a
        LEFT JOIN agent_tools at ON a.id = at.agent_id
        LEFT JOIN tools t ON at.tool_id = t.id
        ${whereClause}
        GROUP BY a.id
        ORDER BY a.updated_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `,
        queryParams,
      );

      return result.rows.map((row) => this.mapDbRowToAgent(row));
    } catch (error) {
      logger.error("Failed to list agents:", error);
      throw error;
    }
  }

  async getRuntimeAgent(id: string): Promise<Agent | null> {
    return this.runtimeAgents.get(id) || null;
  }

  async loadActiveAgents(): Promise<void> {
    try {
      const result = await this.db.query(`
        SELECT a.*, array_agg(t.name) as tool_names
        FROM agents a
        LEFT JOIN agent_tools at ON a.id = at.agent_id
        LEFT JOIN tools t ON at.tool_id = t.id
        WHERE a.is_active = true
        GROUP BY a.id
      `);

      for (const row of result.rows) {
        const agent = this.mapDbRowToAgent(row);
        this.runtimeAgents.set(agent.id, agent);
        await this.stateManager.set(`agent:${agent.id}`, agent);
      }

      logger.info(`Loaded ${result.rows.length} active agents into runtime`);
    } catch (error) {
      logger.error("Failed to load active agents:", error);
    }
  }

  private async updateAgentTools(
    agentId: string,
    toolNames: string[],
  ): Promise<void> {
    await this.db.transaction(async (client) => {
      // Remove existing tools
      await client.query("DELETE FROM agent_tools WHERE agent_id = $1", [
        agentId,
      ]);

      // Add new tools
      if (toolNames.length > 0) {
        const toolResult = await client.query(
          "SELECT id, name FROM tools WHERE name = ANY($1) AND is_active = true",
          [toolNames],
        );

        for (const tool of toolResult.rows) {
          await client.query(
            "INSERT INTO agent_tools (agent_id, tool_id) VALUES ($1, $2)",
            [agentId, tool.id],
          );
        }
      }
    });
  }

  private mapDbRowToAgent(row: any): Agent {
    return {
      id: row.id,
      name: row.name,
      description: row.description || "",
      provider: row.provider,
      hasMemory: row.has_memory,
      tools: row.tool_names ? row.tool_names.filter(Boolean) : [],
      promptTemplate: row.prompt_template,
      configuration: row.configuration || {},
      createdBy: row.created_by,
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString(),
      isActive: row.is_active,
      version: row.version,
    };
  }

  async execute(
    agentId: string,
    input: string,
    sessionId: string,
    context?: Record<string, any>,
    streaming: boolean = false,
  ): Promise<any> {
    try {
      const agent = await this.read(agentId);
      if (!agent) {
        throw new Error(`Agent not found: ${agentId}`);
      }

      // Get or create session
      const session = await this.getOrCreateSession(
        agentId,
        sessionId,
        context,
      );

      // Prepare prompt with context injection
      const enhancedPrompt = this.injectContext(agent.promptTemplate, {
        user_input: input,
        session_context: session.context,
        memory: session.sessionData,
        ...context,
      });

      // Execute with streaming support
      if (streaming) {
        return this.executeStreaming(agent, enhancedPrompt, sessionId);
      } else {
        return this.executeSingle(agent, enhancedPrompt, sessionId);
      }
    } catch (error) {
      logger.error(`Failed to execute agent ${agentId}:`, error);
      throw error;
    }
  }

  private async getOrCreateSession(
    agentId: string,
    sessionId: string,
    context?: Record<string, any>,
  ): Promise<any> {
    try {
      // Check if session exists in Redis
      const existingSession = await this.stateManager.get(
        `session:${sessionId}`,
      );
      if (existingSession) {
        return existingSession;
      }

      // Create new session
      const session = {
        id: sessionId,
        agentId,
        context: context || {},
        sessionData: {},
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true,
      };

      // Store in Redis with TTL
      await this.stateManager.set(`session:${sessionId}`, session, 3600); // 1 hour TTL

      // Store in database
      await this.db.query(
        `
        INSERT INTO agent_sessions (id, agent_id, session_data, context, expires_at)
        VALUES ($1, $2, $3, $4, $5)
      `,
        [
          sessionId,
          agentId,
          JSON.stringify(session.sessionData),
          JSON.stringify(session.context),
          new Date(Date.now() + 3600000), // 1 hour from now
        ],
      );

      // Emit session started event
      this.eventBus.emitSystemEvent("session_started", {
        sessionId,
        agentId,
        context,
      });

      return session;
    } catch (error) {
      logger.error(`Failed to get or create session ${sessionId}:`, error);
      throw error;
    }
  }

  private injectContext(
    template: string,
    context: Record<string, any>,
  ): string {
    let result = template;
    for (const [key, value] of Object.entries(context)) {
      const placeholder = `{{${key}}}`;
      const replacement =
        typeof value === "string" ? value : JSON.stringify(value);
      result = result.replace(new RegExp(placeholder, "g"), replacement);
    }
    return result;
  }

  private async executeStreaming(
    agent: Agent,
    prompt: string,
    sessionId: string,
  ): Promise<any> {
    try {
      const provider = this.providerFactory.getProvider(agent.provider);
      if (!provider) {
        throw new Error(`Provider ${agent.provider} not available`);
      }

      const messageId = `msg_${Date.now()}`;
      const messages = this.buildMessages(agent, prompt);

      await provider.createStreamingCompletion(
        {
          messages,
          model: agent.configuration?.model,
          maxTokens: agent.configuration?.maxTokens || 1000,
          temperature: agent.configuration?.temperature || 0.7,
        },
        (chunk) => {
          // Emit text chunk event
          this.eventBus.emit("stream:text_chunk", {
            chunk: chunk.content,
            isComplete: chunk.isComplete,
            agentId: agent.id,
            sessionId,
            messageId,
            finishReason: chunk.finishReason,
          });
        }
      );

      return {
        messageId,
        streaming: true,
        status: "completed",
      };
    } catch (error) {
      logger.error(`Failed to execute streaming for agent ${agent.id}:`, error);
      throw error;
    }
  }

  private async executeSingle(
    agent: Agent,
    prompt: string,
    sessionId: string,
  ): Promise<any> {
    try {
      const provider = this.providerFactory.getProvider(agent.provider);
      if (!provider) {
        throw new Error(`Provider ${agent.provider} not available`);
      }

      const messages = this.buildMessages(agent, prompt);

      const completion = await provider.createCompletion({
        messages,
        model: agent.configuration?.model,
        maxTokens: agent.configuration?.maxTokens || 1000,
        temperature: agent.configuration?.temperature || 0.7,
      });

      // Update session state
      await this.updateSessionState(sessionId, {
        lastResponse: completion.content,
        lastActivity: new Date().toISOString(),
        usage: completion.usage,
      });

      return {
        response: completion.content,
        agentId: agent.id,
        sessionId,
        messageId: completion.id,
        model: completion.model,
        usage: completion.usage,
        finishReason: completion.finishReason,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error(`Failed to execute single completion for agent ${agent.id}:`, error);
      throw error;
    }
  }

  private async updateSessionState(
    sessionId: string,
    updates: Record<string, any>,
  ): Promise<void> {
    try {
      const session = await this.stateManager.get(`session:${sessionId}`);
      if (session) {
        const updatedSession = {
          ...session,
          sessionData: {
            ...session.sessionData,
            ...updates,
          },
          lastActivity: new Date().toISOString(),
        };

        await this.stateManager.set(
          `session:${sessionId}`,
          updatedSession,
          3600,
        );

        // Emit state update event
        this.eventBus.emit("stream:state_update", {
          sessionId,
          agentId: session.agentId,
          state: updatedSession.sessionData,
          context: updatedSession.context,
        });
      }
    } catch (error) {
      logger.error(`Failed to update session state ${sessionId}:`, error);
    }
  }

  private buildMessages(agent: Agent, userInput: string): LLMMessage[] {
    const messages: LLMMessage[] = [];

    // Add system message if prompt template exists
    if (agent.promptTemplate) {
      messages.push({
        role: "system",
        content: agent.promptTemplate,
      });
    }

    // Add user message
    messages.push({
      role: "user",
      content: userInput,
    });

    return messages;
  }

  getStats(): any {
    return {
      totalRuntime: this.runtimeAgents.size,
      runtimeAgents: Array.from(this.runtimeAgents.keys()),
    };
  }
}
