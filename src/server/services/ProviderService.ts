import { Database } from "../database/Database";
import { StateManager } from "../core/StateManager";
import { EventBus } from "../core/EventBus";
import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";
import { ProviderFactory } from "../providers/ProviderFactory";
import bcrypt from "bcryptjs";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface Provider {
  id: string;
  name: string;
  displayName: string;
  enabled: boolean;
  priority: number;
  fallbackEnabled: boolean;
  maxRetries: number;
  configuration: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ProviderConfig {
  apiKey: string;
  enabled: boolean;
  priority: number;
  fallbackEnabled: boolean;
  maxRetries: number;
}

export interface ProviderStatus {
  name: string;
  status: "online" | "offline" | "error" | "unknown";
  latency?: number;
  usageCount: number;
  errorRate: number;
  lastChecked: string;
}

export class ProviderService {
  private db: Database;
  private stateManager: StateManager;
  private eventBus: EventBus;
  private providerFactory: ProviderFactory;
  private providerStatuses: Map<string, ProviderStatus> = new Map();

  constructor(stateManager: StateManager, eventBus: EventBus) {
    this.db = Database.getInstance();
    this.stateManager = stateManager;
    this.eventBus = eventBus;
    this.providerFactory = ProviderFactory.getInstance();
    this.initializeProviderStatuses();
  }

  async configure(name: string, providerConfig: ProviderConfig): Promise<Provider> {
    try {
      // Use ConfigManager for secure API key encryption
      const encryptedApiKey = config.encryptApiKey(providerConfig.apiKey);

      const result = await this.db.query(
        `
        UPDATE providers
        SET api_key_encrypted = $1, enabled = $2, priority = $3,
            fallback_enabled = $4, max_retries = $5
        WHERE name = $6
        RETURNING *
      `,
        [
          encryptedApiKey,
          providerConfig.enabled,
          providerConfig.priority,
          providerConfig.fallbackEnabled,
          providerConfig.maxRetries,
          name,
        ],
      );

      if (result.rows.length === 0) {
        throw new Error(`Provider not found: ${name}`);
      }

      const provider = this.mapDbRowToProvider(result.rows[0]);

      // Cache in Redis
      await this.stateManager.set(`provider:${name}`, provider);

      // Store API key separately (encrypted)
      await this.stateManager.set(`provider:${name}:apikey`, encryptedApiKey);

      // Test the provider connection if enabled
      if (providerConfig.enabled) {
        const testResult = await this.providerFactory.testProvider(name);
        await this.updateProviderStatus(name, testResult.success ? "online" : "error");
      }

      // Refresh provider factory to pick up new configuration
      this.providerFactory.refreshProviders();

      // Emit event
      this.eventBus.emitProviderEvent("configured", name, provider);

      logger.info(`Provider configured: ${name}`);
      return provider;
    } catch (error) {
      logger.error(`Failed to configure provider ${name}:`, error);
      throw error;
    }
  }

  async getStatus(name: string): Promise<ProviderStatus> {
    try {
      // Check cache first
      if (this.providerStatuses.has(name)) {
        return this.providerStatuses.get(name)!;
      }

      // Query database for latest status
      const result = await this.db.query(
        `
        SELECT ps.*, p.name as provider_name
        FROM provider_status ps
        JOIN providers p ON ps.provider_id = p.id
        WHERE p.name = $1
        ORDER BY ps.checked_at DESC
        LIMIT 1
      `,
        [name],
      );

      let status: ProviderStatus;
      if (result.rows.length > 0) {
        const row = result.rows[0];
        status = {
          name,
          status: row.status,
          latency: row.latency_ms,
          usageCount: 0, // This would come from api_usage table
          errorRate: 0, // This would be calculated
          lastChecked: row.checked_at.toISOString(),
        };
      } else {
        status = {
          name,
          status: "unknown",
          usageCount: 0,
          errorRate: 0,
          lastChecked: new Date().toISOString(),
        };
      }

      this.providerStatuses.set(name, status);
      return status;
    } catch (error) {
      logger.error(`Failed to get provider status ${name}:`, error);
      throw error;
    }
  }

  async test(
    name: string,
  ): Promise<{ success: boolean; latency?: number; error?: string }> {
    try {
      const startTime = Date.now();

      // Use ProviderFactory to test the provider
      const testResult = await this.providerFactory.testProvider(name);

      const latency = Date.now() - startTime;
      const status = testResult.success ? "online" : "error";

      // Update status in database
      await this.updateProviderStatus(name, status, latency, testResult.error);

      // Emit event
      this.eventBus.emitProviderEvent("tested", name, {
        success: testResult.success,
        latency,
        error: testResult.error,
      });

      return {
        success: testResult.success,
        latency,
        error: testResult.error,
      };
    } catch (error) {
      logger.error(`Failed to test provider ${name}:`, error);

      // Update status to error
      await this.updateProviderStatus(name, "error", undefined, error.message);

      return {
        success: false,
        error: error.message,
      };
    }
  }

  async list(): Promise<Provider[]> {
    try {
      const result = await this.db.query(
        "SELECT * FROM providers ORDER BY priority ASC",
      );

      return result.rows.map((row) => this.mapDbRowToProvider(row));
    } catch (error) {
      logger.error("Failed to list providers:", error);
      throw error;
    }
  }

  private async getProvider(name: string): Promise<Provider | null> {
    try {
      // Check cache first
      const cached = await this.stateManager.get(`provider:${name}`);
      if (cached) {
        return cached;
      }

      // Query database
      const result = await this.db.query(
        "SELECT * FROM providers WHERE name = $1",
        [name],
      );

      if (result.rows.length === 0) {
        return null;
      }

      const provider = this.mapDbRowToProvider(result.rows[0]);

      // Cache result
      await this.stateManager.set(`provider:${name}`, provider);

      return provider;
    } catch (error) {
      logger.error(`Failed to get provider ${name}:`, error);
      return null;
    }
  }

  private async updateProviderStatus(
    name: string,
    status: "online" | "offline" | "error" | "unknown",
    latency?: number,
    errorMessage?: string,
  ): Promise<void> {
    try {
      // Get provider ID
      const providerResult = await this.db.query(
        "SELECT id FROM providers WHERE name = $1",
        [name],
      );

      if (providerResult.rows.length === 0) {
        return;
      }

      const providerId = providerResult.rows[0].id;

      // Insert status record
      await this.db.query(
        `
        INSERT INTO provider_status (provider_id, status, latency_ms, error_message)
        VALUES ($1, $2, $3, $4)
      `,
        [providerId, status, latency, errorMessage],
      );

      // Update cache
      const providerStatus: ProviderStatus = {
        name,
        status,
        latency,
        usageCount: 0,
        errorRate: 0,
        lastChecked: new Date().toISOString(),
      };

      this.providerStatuses.set(name, providerStatus);
    } catch (error) {
      logger.error(`Failed to update provider status ${name}:`, error);
    }
  }

  private async testOpenAI(
    apiKey: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch("https://api.openai.com/v1/models", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        return {
          success: false,
          error: `OpenAI API error: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `OpenAI connection error: ${error.message}`,
      };
    }
  }

  private async testClaude(
    apiKey: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch("https://api.anthropic.com/v1/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": apiKey,
          "anthropic-version": "2023-06-01",
        },
        body: JSON.stringify({
          model: "claude-3-haiku-20240307",
          max_tokens: 1,
          messages: [{ role: "user", content: "test" }],
        }),
      });

      if (!response.ok) {
        return {
          success: false,
          error: `Claude API error: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Claude connection error: ${error.message}`,
      };
    }
  }

  private async testGemini(
    apiKey: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`,
      );

      if (!response.ok) {
        return {
          success: false,
          error: `Gemini API error: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Gemini connection error: ${error.message}`,
      };
    }
  }

  private async testMistral(
    apiKey: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch("https://api.mistral.ai/v1/models", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        return {
          success: false,
          error: `Mistral API error: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Mistral connection error: ${error.message}`,
      };
    }
  }

  private async testGroq(
    apiKey: string,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch("https://api.groq.com/openai/v1/models", {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        return {
          success: false,
          error: `Groq API error: ${response.statusText}`,
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Groq connection error: ${error.message}`,
      };
    }
  }

  private async initializeProviderStatuses(): Promise<void> {
    try {
      const providers = ["openai", "claude", "gemini", "mistral", "groq"];

      for (const name of providers) {
        const status = await this.getStatus(name);
        this.providerStatuses.set(name, status);
      }

      logger.info("Provider statuses initialized");
    } catch (error) {
      logger.error("Failed to initialize provider statuses:", error);
    }
  }

  private mapDbRowToProvider(row: any): Provider {
    return {
      id: row.id,
      name: row.name,
      displayName: row.display_name,
      enabled: row.enabled,
      priority: row.priority,
      fallbackEnabled: row.fallback_enabled,
      maxRetries: row.max_retries,
      configuration: row.configuration || {},
      createdAt: row.created_at.toISOString(),
      updatedAt: row.updated_at.toISOString(),
    };
  }

  getStats(): any {
    return {
      totalProviders: this.providerStatuses.size,
      enabledProviders: Array.from(this.providerStatuses.values()).filter(
        (status) => status.status === "online",
      ).length,
      statuses: Object.fromEntries(this.providerStatuses),
    };
  }
}
