import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { AgentService } from "../services/AgentService";
import { StateManager } from "../core/StateManager";
import { EventBus } from "../core/EventBus";
import { Logger } from "../utils/Logger";
import { z } from "zod";
import jwt from "jsonwebtoken";

const logger = Logger.getInstance();

// Validation schemas
const createAgentSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  provider: z.enum(["openai", "claude", "gemini", "mistral", "groq"]),
  hasMemory: z.boolean(),
  tools: z.array(z.string()),
  promptTemplate: z.string().min(1),
  configuration: z.record(z.any()).optional(),
});

const updateAgentSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  provider: z
    .enum(["openai", "claude", "gemini", "mistral", "groq"])
    .optional(),
  hasMemory: z.boolean().optional(),
  tools: z.array(z.string()).optional(),
  promptTemplate: z.string().min(1).optional(),
  configuration: z.record(z.any()).optional(),
});

const listAgentsSchema = z.object({
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional(),
  provider: z.string().optional(),
  hasMemory: z.boolean().optional(),
});

// Authentication middleware
const authenticate = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const token = request.headers.authorization?.replace("Bearer ", "");
    if (!token) {
      reply
        .code(401)
        .send({ success: false, error: "Authentication required" });
      return;
    }

    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key",
    ) as any;
    (request as any).user = decoded;
  } catch (error) {
    reply.code(401).send({ success: false, error: "Invalid token" });
  }
};

// Role-based access control
const authorize = (requiredRoles: string[]) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user;
    if (!user || !requiredRoles.includes(user.role)) {
      reply
        .code(403)
        .send({ success: false, error: "Insufficient permissions" });
      return;
    }
  };
};

export async function agentRoutes(fastify: FastifyInstance) {
  const stateManager = new StateManager(fastify.redis);
  const eventBus = new EventBus();
  const agentService = new AgentService(stateManager, eventBus);

  // Add authentication hook
  fastify.addHook("preHandler", authenticate);

  // Create agent
  fastify.post("/", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const validatedData = createAgentSchema.parse(request.body);
      const userId = (request as any).user?.id;

      const agent = await agentService.create({
        ...validatedData,
        createdBy: userId,
      });

      reply.code(201).send({
        success: true,
        data: agent,
      });
    } catch (error) {
      logger.error("Failed to create agent:", error);

      if (error instanceof z.ZodError) {
        reply.code(400).send({
          success: false,
          error: "Validation error",
          details: error.errors,
        });
      } else {
        reply.code(500).send({
          success: false,
          error: "Internal server error",
        });
      }
    }
  });

  // Get agent by ID
  fastify.get("/:id", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };

      if (!id || typeof id !== "string") {
        reply.code(400).send({
          success: false,
          error: "Invalid agent ID",
        });
        return;
      }

      const agent = await agentService.read(id);

      if (!agent) {
        reply.code(404).send({
          success: false,
          error: "Agent not found",
        });
        return;
      }

      reply.send({
        success: true,
        data: agent,
      });
    } catch (error) {
      logger.error("Failed to get agent:", error);
      reply.code(500).send({
        success: false,
        error: "Internal server error",
      });
    }
  });

  // Update agent
  fastify.put("/:id", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const validatedData = updateAgentSchema.parse(request.body);

      if (!id || typeof id !== "string") {
        reply.code(400).send({
          success: false,
          error: "Invalid agent ID",
        });
        return;
      }

      const agent = await agentService.update(id, validatedData);

      if (!agent) {
        reply.code(404).send({
          success: false,
          error: "Agent not found",
        });
        return;
      }

      reply.send({
        success: true,
        data: agent,
      });
    } catch (error) {
      logger.error("Failed to update agent:", error);

      if (error instanceof z.ZodError) {
        reply.code(400).send({
          success: false,
          error: "Validation error",
          details: error.errors,
        });
      } else {
        reply.code(500).send({
          success: false,
          error: "Internal server error",
        });
      }
    }
  });

  // Delete agent
  fastify.delete(
    "/:id",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { id } = request.params as { id: string };

        if (!id || typeof id !== "string") {
          reply.code(400).send({
            success: false,
            error: "Invalid agent ID",
          });
          return;
        }

        const deleted = await agentService.delete(id);

        if (!deleted) {
          reply.code(404).send({
            success: false,
            error: "Agent not found",
          });
          return;
        }

        reply.send({
          success: true,
          message: "Agent deleted successfully",
        });
      } catch (error) {
        logger.error("Failed to delete agent:", error);
        reply.code(500).send({
          success: false,
          error: "Internal server error",
        });
      }
    },
  );

  // List agents
  fastify.get("/", async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = listAgentsSchema.parse(request.query);
      const userId = (request as any).user?.id;

      const agents = await agentService.list(
        {
          provider: query.provider,
          hasMemory: query.hasMemory,
          createdBy: userId,
        },
        query.limit || 50,
        query.offset || 0,
      );

      reply.send({
        success: true,
        data: agents,
        pagination: {
          limit: query.limit || 50,
          offset: query.offset || 0,
          total: agents.length,
        },
      });
    } catch (error) {
      logger.error("Failed to list agents:", error);

      if (error instanceof z.ZodError) {
        reply.code(400).send({
          success: false,
          error: "Validation error",
          details: error.errors,
        });
      } else {
        reply.code(500).send({
          success: false,
          error: "Internal server error",
        });
      }
    }
  });

  // Get agent stats
  fastify.get(
    "/stats",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const stats = agentService.getStats();
        reply.send({
          success: true,
          data: stats,
        });
      } catch (error) {
        logger.error("Failed to get agent stats:", error);
        reply.code(500).send({
          success: false,
          error: "Internal server error",
        });
      }
    },
  );
}
