import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { z } from "zod";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { Database } from "../database/Database";
import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
  rememberMe: z.boolean().optional(),
});

const registerSchema = z.object({
  name: z.string().min(1).max(100),
  email: z.string().email(),
  password: z.string().min(8),
  subscribeNewsletter: z.boolean().optional(),
});

interface User {
  id: string;
  name: string;
  email: string;
  password_hash: string;
  role: string;
  permissions: string[];
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
  email_verified: boolean;
}

export async function authRoutes(fastify: FastifyInstance) {
  const db = Database.getInstance();

  // Login endpoint
  fastify.post(
    "/login",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { email, password, rememberMe } = loginSchema.parse(request.body);

        // Find user by email
        const result = await db.query(
          "SELECT * FROM users WHERE email = $1 AND is_active = true",
          [email.toLowerCase()],
        );

        if (result.rows.length === 0) {
          reply.code(401).send({
            success: false,
            error: "Invalid email or password",
          });
          return;
        }

        const user: User = result.rows[0];

        // Verify password
        const isValidPassword = await bcrypt.compare(
          password,
          user.password_hash,
        );
        if (!isValidPassword) {
          reply.code(401).send({
            success: false,
            error: "Invalid email or password",
          });
          return;
        }

        // Generate JWT token
        const tokenPayload = {
          id: user.id,
          email: user.email,
          role: user.role,
          permissions: user.permissions,
        };

        const token = jwt.sign(
          tokenPayload,
          process.env.JWT_SECRET || "your-secret-key",
          { expiresIn: rememberMe ? "30d" : "24h" },
        );

        // Update last login
        await db.query("UPDATE users SET last_login = NOW() WHERE id = $1", [
          user.id,
        ]);

        // Log successful login
        logger.info(`User logged in: ${user.email}`);

        reply.send({
          success: true,
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions,
          },
        });
      } catch (error) {
        logger.error("Login error:", error);

        if (error instanceof z.ZodError) {
          reply.code(400).send({
            success: false,
            error: "Invalid input data",
            details: error.errors,
          });
        } else {
          reply.code(500).send({
            success: false,
            error: "Internal server error",
          });
        }
      }
    },
  );

  // Register endpoint
  fastify.post(
    "/register",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { name, email, password, subscribeNewsletter } =
          registerSchema.parse(request.body);

        // Check if user already exists
        const existingUser = await db.query(
          "SELECT id FROM users WHERE email = $1",
          [email.toLowerCase()],
        );

        if (existingUser.rows.length > 0) {
          reply.code(409).send({
            success: false,
            error: "An account with this email already exists",
          });
          return;
        }

        // Hash password
        const passwordHash = await bcrypt.hash(password, 12);

        // Create user
        const result = await db.query(
          `INSERT INTO users (name, email, password_hash, role, permissions, subscribe_newsletter)
         VALUES ($1, $2, $3, $4, $5, $6)
         RETURNING id, name, email, role, permissions`,
          [
            name.trim(),
            email.toLowerCase(),
            passwordHash,
            "user",
            JSON.stringify(["read:own", "write:own"]),
            subscribeNewsletter || false,
          ],
        );

        const newUser = result.rows[0];

        // Log successful registration
        logger.info(`New user registered: ${newUser.email}`);

        reply.code(201).send({
          success: true,
          message: "Account created successfully! You can now sign in.",
        });
      } catch (error) {
        logger.error("Registration error:", error);

        if (error instanceof z.ZodError) {
          reply.code(400).send({
            success: false,
            error: "Invalid input data",
            details: error.errors,
          });
        } else {
          reply.code(500).send({
            success: false,
            error: "Internal server error",
          });
        }
      }
    },
  );

  // Verify token endpoint
  fastify.get(
    "/verify",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const token = request.headers.authorization?.replace("Bearer ", "");

        if (!token) {
          reply.code(401).send({
            success: false,
            error: "No token provided",
          });
          return;
        }

        const decoded = jwt.verify(
          token,
          process.env.JWT_SECRET || "your-secret-key",
        ) as any;

        // Get fresh user data
        const result = await db.query(
          "SELECT id, name, email, role, permissions FROM users WHERE id = $1 AND is_active = true",
          [decoded.id],
        );

        if (result.rows.length === 0) {
          reply.code(401).send({
            success: false,
            error: "User not found or inactive",
          });
          return;
        }

        const user = result.rows[0];

        reply.send({
          success: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            permissions: user.permissions,
          },
        });
      } catch (error) {
        reply.code(401).send({
          success: false,
          error: "Invalid or expired token",
        });
      }
    },
  );

  // Logout endpoint
  fastify.post(
    "/logout",
    async (request: FastifyRequest, reply: FastifyReply) => {
      // In a production system, you might want to blacklist the token
      // For now, we'll just return success as the client will remove the token
      reply.send({
        success: true,
        message: "Logged out successfully",
      });
    },
  );

  // Google OAuth placeholder
  fastify.get(
    "/google",
    async (request: FastifyRequest, reply: FastifyReply) => {
      reply.code(501).send({
        success: false,
        error: "Google OAuth not implemented yet",
      });
    },
  );

  // GitHub OAuth placeholder
  fastify.get(
    "/github",
    async (request: FastifyRequest, reply: FastifyReply) => {
      reply.code(501).send({
        success: false,
        error: "GitHub OAuth not implemented yet",
      });
    },
  );

  // System health endpoint
  fastify.get(
    "/health",
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // Check database connection
        await db.query("SELECT 1");

        reply.send({
          status: "healthy",
          timestamp: new Date().toISOString(),
          services: {
            database: "connected",
            redis: "connected",
          },
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
        });
      } catch (error) {
        reply.code(503).send({
          status: "unhealthy",
          error: "Database connection failed",
        });
      }
    },
  );
}
