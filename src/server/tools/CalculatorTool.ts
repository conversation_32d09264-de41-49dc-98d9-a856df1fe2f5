import { Logger } from "../utils/Logger";

const logger = Logger.getInstance();

export class CalculatorTool {
  private static readonly ALLOWED_OPERATORS = ['+', '-', '*', '/', '(', ')', '.', ' '];
  private static readonly ALLOWED_FUNCTIONS = ['sin', 'cos', 'tan', 'sqrt', 'log', 'abs', 'pow', 'exp'];

  static execute(parameters: { expression: string }): { result: number; expression: string } {
    try {
      const { expression } = parameters;
      
      if (!expression || typeof expression !== 'string') {
        throw new Error('Expression is required and must be a string');
      }

      // Validate and sanitize the expression
      const sanitizedExpression = this.sanitizeExpression(expression);
      
      // Evaluate the expression safely
      const result = this.evaluateExpression(sanitizedExpression);
      
      logger.debug(`Calculator executed: ${expression} = ${result}`);
      
      return {
        result,
        expression: sanitizedExpression,
      };
    } catch (error) {
      logger.error('Calculator tool error:', error);
      throw new Error(`Calculator error: ${error.message}`);
    }
  }

  private static sanitizeExpression(expression: string): string {
    // Remove any characters that are not numbers, operators, or allowed functions
    let sanitized = expression.toLowerCase().trim();
    
    // Replace common mathematical constants
    sanitized = sanitized.replace(/\bpi\b/g, Math.PI.toString());
    sanitized = sanitized.replace(/\be\b/g, Math.E.toString());
    
    // Validate characters
    for (let i = 0; i < sanitized.length; i++) {
      const char = sanitized[i];
      if (!this.isAllowedCharacter(char)) {
        throw new Error(`Invalid character in expression: ${char}`);
      }
    }

    // Check for allowed functions and replace with Math equivalents
    for (const func of this.ALLOWED_FUNCTIONS) {
      const regex = new RegExp(`\\b${func}\\b`, 'g');
      sanitized = sanitized.replace(regex, `Math.${func}`);
    }

    return sanitized;
  }

  private static isAllowedCharacter(char: string): boolean {
    // Allow digits
    if (/\d/.test(char)) return true;
    
    // Allow operators
    if (this.ALLOWED_OPERATORS.includes(char)) return true;
    
    // Allow letters (for function names)
    if (/[a-z]/.test(char)) return true;
    
    return false;
  }

  private static evaluateExpression(expression: string): number {
    try {
      // Use a safe evaluation approach
      const result = this.safeEval(expression);
      
      if (typeof result !== 'number') {
        throw new Error('Expression did not evaluate to a number');
      }
      
      if (!isFinite(result)) {
        throw new Error('Expression resulted in infinity or NaN');
      }
      
      return result;
    } catch (error) {
      throw new Error(`Evaluation error: ${error.message}`);
    }
  }

  private static safeEval(expression: string): number {
    // Create a safe evaluation context
    const allowedGlobals = {
      Math,
      parseInt,
      parseFloat,
      Number,
      isNaN,
      isFinite,
    };

    // Create a function that evaluates the expression in a restricted context
    try {
      const func = new Function(
        ...Object.keys(allowedGlobals),
        `"use strict"; return (${expression});`
      );
      
      return func(...Object.values(allowedGlobals));
    } catch (error) {
      throw new Error(`Invalid mathematical expression: ${error.message}`);
    }
  }

  // Additional mathematical operations
  static add(a: number, b: number): number {
    return a + b;
  }

  static subtract(a: number, b: number): number {
    return a - b;
  }

  static multiply(a: number, b: number): number {
    return a * b;
  }

  static divide(a: number, b: number): number {
    if (b === 0) {
      throw new Error('Division by zero is not allowed');
    }
    return a / b;
  }

  static power(base: number, exponent: number): number {
    return Math.pow(base, exponent);
  }

  static sqrt(value: number): number {
    if (value < 0) {
      throw new Error('Square root of negative number is not allowed');
    }
    return Math.sqrt(value);
  }

  static percentage(value: number, percentage: number): number {
    return (value * percentage) / 100;
  }

  // Validate tool schema
  static getSchema(): any {
    return {
      input: {
        expression: {
          type: "string",
          description: "Mathematical expression to evaluate",
          required: true,
          examples: ["2 + 2", "sqrt(16)", "sin(pi/2)", "10 * (5 + 3)"]
        }
      },
      output: {
        result: {
          type: "number",
          description: "The calculated result"
        },
        expression: {
          type: "string",
          description: "The sanitized expression that was evaluated"
        }
      }
    };
  }
}
