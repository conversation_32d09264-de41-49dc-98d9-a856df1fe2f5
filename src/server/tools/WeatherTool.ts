import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface WeatherData {
  location: string;
  country: string;
  temperature: number;
  feelsLike: number;
  humidity: number;
  pressure: number;
  windSpeed: number;
  windDirection: string;
  visibility: number;
  uvIndex: number;
  conditions: string;
  description: string;
  icon: string;
  timestamp: string;
  units: string;
}

export interface WeatherResponse {
  current: WeatherData;
  provider: string;
  requestTime: number;
}

export class WeatherTool {
  static async execute(parameters: { 
    location: string; 
    units?: 'metric' | 'imperial';
  }): Promise<WeatherResponse> {
    try {
      const { location, units = config.get("WEATHER_UNITS") as 'metric' | 'imperial' } = parameters;
      
      if (!location || typeof location !== 'string') {
        throw new Error('Location is required and must be a string');
      }

      if (location.trim().length === 0) {
        throw new Error('Location cannot be empty');
      }

      const provider = config.get("WEATHER_PROVIDER");
      const startTime = Date.now();

      let weatherData: WeatherData;
      
      switch (provider) {
        case 'openweather':
          weatherData = await this.getWeatherFromOpenWeather(location, units);
          break;
        case 'weatherapi':
          weatherData = await this.getWeatherFromWeatherAPI(location, units);
          break;
        default:
          throw new Error(`Unsupported weather provider: ${provider}`);
      }

      const requestTime = Date.now() - startTime;

      logger.debug(`Weather data retrieved for "${location}" in ${requestTime}ms`);

      return {
        current: weatherData,
        provider,
        requestTime,
      };
    } catch (error) {
      logger.error('Weather tool error:', error);
      throw new Error(`Weather error: ${error.message}`);
    }
  }

  private static async getWeatherFromOpenWeather(
    location: string, 
    units: 'metric' | 'imperial'
  ): Promise<WeatherData> {
    const apiKey = config.getToolApiKey('openweather');
    if (!apiKey) {
      throw new Error('OpenWeatherMap API key not configured');
    }

    const response = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(location)}&appid=${apiKey}&units=${units}`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenWeatherMap API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
    }

    const data = await response.json();

    return {
      location: data.name,
      country: data.sys.country,
      temperature: Math.round(data.main.temp),
      feelsLike: Math.round(data.main.feels_like),
      humidity: data.main.humidity,
      pressure: data.main.pressure,
      windSpeed: data.wind?.speed || 0,
      windDirection: this.getWindDirection(data.wind?.deg || 0),
      visibility: data.visibility ? Math.round(data.visibility / 1000) : 0,
      uvIndex: 0, // OpenWeatherMap requires separate UV API call
      conditions: data.weather[0].main,
      description: data.weather[0].description,
      icon: data.weather[0].icon,
      timestamp: new Date().toISOString(),
      units,
    };
  }

  private static async getWeatherFromWeatherAPI(
    location: string, 
    units: 'metric' | 'imperial'
  ): Promise<WeatherData> {
    const apiKey = config.getToolApiKey('weatherapi');
    if (!apiKey) {
      throw new Error('WeatherAPI key not configured');
    }

    const response = await fetch(
      `https://api.weatherapi.com/v1/current.json?key=${apiKey}&q=${encodeURIComponent(location)}&aqi=no`
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`WeatherAPI error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();

    // Convert units if needed
    const isMetric = units === 'metric';
    const temperature = isMetric ? data.current.temp_c : data.current.temp_f;
    const feelsLike = isMetric ? data.current.feelslike_c : data.current.feelslike_f;
    const windSpeed = isMetric ? data.current.wind_kph : data.current.wind_mph;
    const visibility = isMetric ? data.current.vis_km : data.current.vis_miles;

    return {
      location: data.location.name,
      country: data.location.country,
      temperature: Math.round(temperature),
      feelsLike: Math.round(feelsLike),
      humidity: data.current.humidity,
      pressure: isMetric ? data.current.pressure_mb : data.current.pressure_in,
      windSpeed: Math.round(windSpeed),
      windDirection: data.current.wind_dir,
      visibility: Math.round(visibility),
      uvIndex: data.current.uv,
      conditions: data.current.condition.text,
      description: data.current.condition.text,
      icon: data.current.condition.icon,
      timestamp: new Date().toISOString(),
      units,
    };
  }

  private static getWindDirection(degrees: number): string {
    const directions = [
      'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
      'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
    ];
    
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }

  // Utility methods
  static convertTemperature(temp: number, from: 'C' | 'F', to: 'C' | 'F'): number {
    if (from === to) return temp;
    
    if (from === 'C' && to === 'F') {
      return (temp * 9/5) + 32;
    } else if (from === 'F' && to === 'C') {
      return (temp - 32) * 5/9;
    }
    
    return temp;
  }

  static formatTemperature(temp: number, units: 'metric' | 'imperial'): string {
    const symbol = units === 'metric' ? '°C' : '°F';
    return `${Math.round(temp)}${symbol}`;
  }

  static getWeatherEmoji(conditions: string): string {
    const conditionMap: Record<string, string> = {
      'clear': '☀️',
      'sunny': '☀️',
      'partly cloudy': '⛅',
      'cloudy': '☁️',
      'overcast': '☁️',
      'rain': '🌧️',
      'drizzle': '🌦️',
      'snow': '❄️',
      'thunderstorm': '⛈️',
      'fog': '🌫️',
      'mist': '🌫️',
      'wind': '💨',
    };

    const condition = conditions.toLowerCase();
    for (const [key, emoji] of Object.entries(conditionMap)) {
      if (condition.includes(key)) {
        return emoji;
      }
    }
    
    return '🌤️'; // Default weather emoji
  }

  // Validate tool schema
  static getSchema(): any {
    return {
      input: {
        location: {
          type: "string",
          description: "Location to get weather for (city, country, coordinates, etc.)",
          required: true,
          examples: ["New York", "London, UK", "40.7128,-74.0060", "Tokyo, Japan"]
        },
        units: {
          type: "string",
          description: "Temperature units to use",
          required: false,
          enum: ["metric", "imperial"],
          default: "metric"
        }
      },
      output: {
        current: {
          type: "object",
          description: "Current weather data",
          properties: {
            location: { type: "string" },
            country: { type: "string" },
            temperature: { type: "number" },
            feelsLike: { type: "number" },
            humidity: { type: "number" },
            pressure: { type: "number" },
            windSpeed: { type: "number" },
            windDirection: { type: "string" },
            visibility: { type: "number" },
            uvIndex: { type: "number" },
            conditions: { type: "string" },
            description: { type: "string" },
            icon: { type: "string" },
            timestamp: { type: "string" },
            units: { type: "string" }
          }
        },
        provider: {
          type: "string",
          description: "Weather provider used"
        },
        requestTime: {
          type: "number",
          description: "Time taken to fetch weather data in milliseconds"
        }
      }
    };
  }
}
