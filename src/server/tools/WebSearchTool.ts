import { Logger } from "../utils/Logger";
import { ConfigManager } from "../utils/ConfigManager";

const logger = Logger.getInstance();
const config = ConfigManager.getInstance();

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  position: number;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  totalResults: number;
  searchTime: number;
  provider: string;
}

export class WebSearchTool {
  private static readonly MAX_RESULTS = 20;
  private static readonly DEFAULT_RESULTS = 10;

  static async execute(parameters: { 
    query: string; 
    limit?: number; 
    safeSearch?: boolean;
  }): Promise<SearchResponse> {
    try {
      const { query, limit = this.DEFAULT_RESULTS, safeSearch = true } = parameters;
      
      if (!query || typeof query !== 'string') {
        throw new Error('Query is required and must be a string');
      }

      if (query.trim().length === 0) {
        throw new Error('Query cannot be empty');
      }

      if (limit > this.MAX_RESULTS) {
        throw new Error(`Limit cannot exceed ${this.MAX_RESULTS}`);
      }

      const provider = config.get("WEB_SEARCH_PROVIDER");
      const startTime = Date.now();

      let results: SearchResult[];
      
      switch (provider) {
        case 'serper':
          results = await this.searchWithSerper(query, limit, safeSearch);
          break;
        case 'serpapi':
          results = await this.searchWithSerpAPI(query, limit, safeSearch);
          break;
        default:
          throw new Error(`Unsupported search provider: ${provider}`);
      }

      const searchTime = Date.now() - startTime;

      logger.debug(`Web search executed: "${query}" returned ${results.length} results in ${searchTime}ms`);

      return {
        query,
        results,
        totalResults: results.length,
        searchTime,
        provider,
      };
    } catch (error) {
      logger.error('Web search tool error:', error);
      throw new Error(`Web search error: ${error.message}`);
    }
  }

  private static async searchWithSerper(
    query: string, 
    limit: number, 
    safeSearch: boolean
  ): Promise<SearchResult[]> {
    const apiKey = config.getToolApiKey('serper');
    if (!apiKey) {
      throw new Error('Serper API key not configured');
    }

    const response = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: query,
        num: limit,
        safe: safeSearch ? 'active' : 'off',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Serper API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.organic) {
      return [];
    }

    return data.organic.map((result: any, index: number) => ({
      title: result.title || 'No title',
      url: result.link || '',
      snippet: result.snippet || 'No description available',
      position: index + 1,
    }));
  }

  private static async searchWithSerpAPI(
    query: string, 
    limit: number, 
    safeSearch: boolean
  ): Promise<SearchResult[]> {
    const apiKey = config.getToolApiKey('serpapi');
    if (!apiKey) {
      throw new Error('SerpAPI key not configured');
    }

    const params = new URLSearchParams({
      engine: 'google',
      q: query,
      api_key: apiKey,
      num: limit.toString(),
      safe: safeSearch ? 'active' : 'off',
    });

    const response = await fetch(`https://serpapi.com/search?${params}`);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SerpAPI error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`SerpAPI error: ${data.error}`);
    }

    if (!data.organic_results) {
      return [];
    }

    return data.organic_results.map((result: any, index: number) => ({
      title: result.title || 'No title',
      url: result.link || '',
      snippet: result.snippet || 'No description available',
      position: index + 1,
    }));
  }

  // Utility methods for search query processing
  static sanitizeQuery(query: string): string {
    // Remove potentially harmful characters and normalize
    return query
      .trim()
      .replace(/[<>]/g, '') // Remove HTML brackets
      .replace(/['"]/g, '') // Remove quotes that might break API calls
      .substring(0, 500); // Limit query length
  }

  static buildSearchQuery(terms: string[], operator: 'AND' | 'OR' = 'AND'): string {
    if (terms.length === 0) return '';
    if (terms.length === 1) return this.sanitizeQuery(terms[0]);
    
    const sanitizedTerms = terms.map(term => this.sanitizeQuery(term));
    return sanitizedTerms.join(operator === 'AND' ? ' ' : ' OR ');
  }

  static addSiteFilter(query: string, site: string): string {
    const sanitizedSite = site.replace(/[^a-zA-Z0-9.-]/g, '');
    return `${this.sanitizeQuery(query)} site:${sanitizedSite}`;
  }

  static addDateFilter(query: string, timeframe: 'day' | 'week' | 'month' | 'year'): string {
    // Note: This is a basic implementation. Different search providers handle date filters differently.
    const dateMap = {
      day: 'past 24 hours',
      week: 'past week',
      month: 'past month',
      year: 'past year',
    };
    
    return `${this.sanitizeQuery(query)} ${dateMap[timeframe]}`;
  }

  // Validate tool schema
  static getSchema(): any {
    return {
      input: {
        query: {
          type: "string",
          description: "Search query to execute",
          required: true,
          examples: ["artificial intelligence", "weather in New York", "latest news technology"]
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return (1-20)",
          required: false,
          default: 10,
          minimum: 1,
          maximum: 20
        },
        safeSearch: {
          type: "boolean",
          description: "Enable safe search filtering",
          required: false,
          default: true
        }
      },
      output: {
        query: {
          type: "string",
          description: "The search query that was executed"
        },
        results: {
          type: "array",
          description: "Array of search results",
          items: {
            type: "object",
            properties: {
              title: { type: "string" },
              url: { type: "string" },
              snippet: { type: "string" },
              position: { type: "number" }
            }
          }
        },
        totalResults: {
          type: "number",
          description: "Total number of results returned"
        },
        searchTime: {
          type: "number",
          description: "Time taken to execute search in milliseconds"
        },
        provider: {
          type: "string",
          description: "Search provider used"
        }
      }
    };
  }
}
