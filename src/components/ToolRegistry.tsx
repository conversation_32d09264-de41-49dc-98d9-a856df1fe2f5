import React, { useState } from "react";
import { Plus<PERSON><PERSON>cle, Search, Edit, Trash2, Play, Check, X } from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface Tool {
  id: string;
  name: string;
  description: string;
  type: "internal" | "external";
  schema: string;
  endpoint?: string;
  active: boolean;
}

export default function ToolRegistry() {
  const [tools, setTools] = useState<Tool[]>([
    {
      id: "1",
      name: "WebSearch",
      description: "Search the web for information",
      type: "external",
      schema: '{"input":{"query":"string"},"output":{"results":"array"}}',
      endpoint: "https://api.search.com/v1/search",
      active: true,
    },
    {
      id: "2",
      name: "Calculator",
      description: "Perform mathematical calculations",
      type: "internal",
      schema: '{"input":{"expression":"string"},"output":{"result":"number"}}',
      active: true,
    },
    {
      id: "3",
      name: "WeatherAPI",
      description: "Get current weather information",
      type: "external",
      schema:
        '{"input":{"location":"string"},"output":{"temperature":"number","conditions":"string"}}',
      endpoint: "https://api.weather.com/v1/current",
      active: false,
    },
  ]);

  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [currentTool, setCurrentTool] = useState<Tool | null>(null);
  const [newTool, setNewTool] = useState<Partial<Tool>>({
    name: "",
    description: "",
    type: "internal",
    schema: "",
    endpoint: "",
    active: true,
  });

  const filteredTools = tools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleAddTool = () => {
    const tool = {
      ...newTool,
      id: Date.now().toString(),
    } as Tool;

    setTools([...tools, tool]);
    setNewTool({
      name: "",
      description: "",
      type: "internal",
      schema: "",
      endpoint: "",
      active: true,
    });
    setIsAddDialogOpen(false);
  };

  const handleEditTool = () => {
    if (!currentTool) return;

    setTools(
      tools.map((tool) => (tool.id === currentTool.id ? currentTool : tool)),
    );
    setIsEditDialogOpen(false);
  };

  const handleDeleteTool = () => {
    if (!currentTool) return;

    setTools(tools.filter((tool) => tool.id !== currentTool.id));
    setIsDeleteDialogOpen(false);
  };

  const handleTestTool = () => {
    // In a real implementation, this would send a request to test the tool
    console.log("Testing tool:", currentTool);
    // Simulate a successful test
    setTimeout(() => {
      setIsTestDialogOpen(false);
    }, 1500);
  };

  return (
    <div className="bg-background p-6 rounded-lg shadow-sm w-full">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Tool Registry</CardTitle>
              <CardDescription>
                Manage and configure tools for agent use
              </CardDescription>
            </div>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Tool
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-6">
            <Search className="mr-2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tools..."
              className="max-w-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTools.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={5}
                      className="text-center text-muted-foreground py-6"
                    >
                      No tools found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredTools.map((tool) => (
                    <TableRow key={tool.id}>
                      <TableCell className="font-medium">{tool.name}</TableCell>
                      <TableCell>{tool.description}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            tool.type === "internal" ? "secondary" : "outline"
                          }
                        >
                          {tool.type === "internal" ? "Internal" : "External"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={tool.active ? "default" : "destructive"}
                        >
                          {tool.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setCurrentTool(tool);
                              setIsTestDialogOpen(true);
                            }}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setCurrentTool(tool);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setCurrentTool(tool);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Tool Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Tool</DialogTitle>
            <DialogDescription>
              Create a new tool for agents to use. Define its schema and
              execution type.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                className="col-span-3"
                value={newTool.name}
                onChange={(e) =>
                  setNewTool({ ...newTool, name: e.target.value })
                }
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Input
                id="description"
                className="col-span-3"
                value={newTool.description}
                onChange={(e) =>
                  setNewTool({ ...newTool, description: e.target.value })
                }
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type
              </Label>
              <Select
                value={newTool.type}
                onValueChange={(value) =>
                  setNewTool({
                    ...newTool,
                    type: value as "internal" | "external",
                  })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal">Internal</SelectItem>
                  <SelectItem value="external">External</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newTool.type === "external" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="endpoint" className="text-right">
                  Endpoint
                </Label>
                <Input
                  id="endpoint"
                  className="col-span-3"
                  value={newTool.endpoint}
                  onChange={(e) =>
                    setNewTool({ ...newTool, endpoint: e.target.value })
                  }
                />
              </div>
            )}

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="schema" className="text-right pt-2">
                Schema
              </Label>
              <Textarea
                id="schema"
                className="col-span-3 min-h-[150px] font-mono text-sm"
                placeholder='{"input":{"param":"type"},"output":{"result":"type"}}'
                value={newTool.schema}
                onChange={(e) =>
                  setNewTool({ ...newTool, schema: e.target.value })
                }
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="active" className="text-right">
                Active
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={newTool.active}
                  onCheckedChange={(checked) =>
                    setNewTool({ ...newTool, active: checked })
                  }
                />
                <Label htmlFor="active">
                  {newTool.active ? "Active" : "Inactive"}
                </Label>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Retry Configuration</Label>
              <div className="col-span-3 space-y-4 p-4 border rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="maxRetries">Max Retries</Label>
                    <Input
                      id="maxRetries"
                      type="number"
                      placeholder="3"
                      min="0"
                      max="10"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="retryDelay">Retry Delay (ms)</Label>
                    <Input
                      id="retryDelay"
                      type="number"
                      placeholder="1000"
                      min="100"
                      max="10000"
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="fallbackMessage">Fallback Message</Label>
                  <Textarea
                    id="fallbackMessage"
                    placeholder="Tool execution failed. Please try again later."
                    className="min-h-[80px]"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="exponentialBackoff" />
                  <Label htmlFor="exponentialBackoff">
                    Use exponential backoff
                  </Label>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Timeout Configuration</Label>
              <div className="col-span-3 space-y-4 p-4 border rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="executionTimeout">
                      Execution Timeout (ms)
                    </Label>
                    <Input
                      id="executionTimeout"
                      type="number"
                      placeholder="30000"
                      min="1000"
                      max="300000"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="connectionTimeout">
                      Connection Timeout (ms)
                    </Label>
                    <Input
                      id="connectionTimeout"
                      type="number"
                      placeholder="5000"
                      min="1000"
                      max="30000"
                    />
                  </div>
                </div>
              </div>
            </div>

            {newTool.type === "external" && (
              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">API Integration</Label>
                <div className="col-span-3 space-y-4 p-4 border rounded-lg">
                  <div className="grid gap-2">
                    <Label htmlFor="authType">Authentication Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select authentication type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="bearer">Bearer Token</SelectItem>
                        <SelectItem value="apikey">API Key</SelectItem>
                        <SelectItem value="basic">Basic Auth</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="headers">Custom Headers (JSON)</Label>
                    <Textarea
                      id="headers"
                      placeholder='{"Content-Type": "application/json"}'
                      className="font-mono text-sm"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="validateSsl" defaultChecked />
                    <Label htmlFor="validateSsl">
                      Validate SSL certificates
                    </Label>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTool}>Add Tool</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Tool Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
            <DialogDescription>
              Modify the tool's configuration and schema.
            </DialogDescription>
          </DialogHeader>

          {currentTool && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  className="col-span-3"
                  value={currentTool.name}
                  onChange={(e) =>
                    setCurrentTool({ ...currentTool, name: e.target.value })
                  }
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-description" className="text-right">
                  Description
                </Label>
                <Input
                  id="edit-description"
                  className="col-span-3"
                  value={currentTool.description}
                  onChange={(e) =>
                    setCurrentTool({
                      ...currentTool,
                      description: e.target.value,
                    })
                  }
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-type" className="text-right">
                  Type
                </Label>
                <Select
                  value={currentTool.type}
                  onValueChange={(value) =>
                    setCurrentTool({
                      ...currentTool,
                      type: value as "internal" | "external",
                    })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="internal">Internal</SelectItem>
                    <SelectItem value="external">External</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {currentTool.type === "external" && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-endpoint" className="text-right">
                    Endpoint
                  </Label>
                  <Input
                    id="edit-endpoint"
                    className="col-span-3"
                    value={currentTool.endpoint}
                    onChange={(e) =>
                      setCurrentTool({
                        ...currentTool,
                        endpoint: e.target.value,
                      })
                    }
                  />
                </div>
              )}

              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="edit-schema" className="text-right pt-2">
                  Schema
                </Label>
                <Textarea
                  id="edit-schema"
                  className="col-span-3 min-h-[150px] font-mono text-sm"
                  value={currentTool.schema}
                  onChange={(e) =>
                    setCurrentTool({ ...currentTool, schema: e.target.value })
                  }
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-active" className="text-right">
                  Active
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-active"
                    checked={currentTool.active}
                    onCheckedChange={(checked) =>
                      setCurrentTool({ ...currentTool, active: checked })
                    }
                  />
                  <Label htmlFor="edit-active">
                    {currentTool.active ? "Active" : "Inactive"}
                  </Label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleEditTool}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Tool Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Tool</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this tool? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>

          {currentTool && (
            <div className="py-4">
              <p className="font-medium">{currentTool.name}</p>
              <p className="text-sm text-muted-foreground">
                {currentTool.description}
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTool}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Test Tool Dialog */}
      <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Test Tool</DialogTitle>
            <DialogDescription>
              Test the tool with sample input parameters.
            </DialogDescription>
          </DialogHeader>

          {currentTool && (
            <div className="space-y-4 py-4">
              <div className="flex items-center space-x-2">
                <Badge
                  variant={
                    currentTool.type === "internal" ? "secondary" : "outline"
                  }
                >
                  {currentTool.type === "internal" ? "Internal" : "External"}
                </Badge>
                <span className="font-medium">{currentTool.name}</span>
              </div>

              <Tabs defaultValue="input">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="input">Input</TabsTrigger>
                  <TabsTrigger value="output">Output</TabsTrigger>
                  <TabsTrigger value="schema">Schema</TabsTrigger>
                </TabsList>
                <TabsContent value="input" className="space-y-4">
                  <Textarea
                    className="min-h-[150px] font-mono text-sm"
                    placeholder="{\n  // Enter test input parameters here\n}"
                  />
                </TabsContent>
                <TabsContent value="output" className="space-y-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="font-mono text-sm whitespace-pre-wrap">
                      {/* This would show the actual output from the tool test */}
                      {JSON.stringify(
                        {
                          result:
                            "Sample output will appear here after testing",
                        },
                        null,
                        2,
                      )}
                    </pre>
                  </div>
                </TabsContent>
                <TabsContent value="schema" className="space-y-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="font-mono text-sm whitespace-pre-wrap">
                      {JSON.stringify(
                        JSON.parse(currentTool.schema || "{}"),
                        null,
                        2,
                      )}
                    </pre>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsTestDialogOpen(false)}
            >
              Close
            </Button>
            <Button onClick={handleTestTool}>Run Test</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
