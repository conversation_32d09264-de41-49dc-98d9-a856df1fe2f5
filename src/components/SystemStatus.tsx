import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Activity,
  Server,
  Database,
  Wifi,
  AlertCircle,
  CheckCircle,
  RefreshCw,
} from "lucide-react";

interface SystemMetrics {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  services: {
    redis: string;
    database: string;
    websocket: string;
    http: string;
  };
  metrics: {
    connectedClients: number;
    activeAgents: number;
    registeredTools: number;
    enabledProviders: number;
  };
  performance: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    uptime: number;
  };
}

const SystemStatus = () => {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    status: "healthy",
    timestamp: new Date().toISOString(),
    services: {
      redis: "connected",
      database: "connected",
      websocket: "connected",
      http: "connected",
    },
    metrics: {
      connectedClients: 12,
      activeAgents: 5,
      registeredTools: 8,
      enabledProviders: 3,
    },
    performance: {
      cpuUsage: 24,
      memoryUsage: 45,
      diskUsage: 32,
      uptime: 86400, // 24 hours in seconds
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  const refreshMetrics = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would fetch from the backend API
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Simulate some metric changes
      setMetrics((prev) => ({
        ...prev,
        timestamp: new Date().toISOString(),
        metrics: {
          ...prev.metrics,
          connectedClients: Math.floor(Math.random() * 20) + 5,
        },
        performance: {
          ...prev.performance,
          cpuUsage: Math.floor(Math.random() * 40) + 10,
          memoryUsage: Math.floor(Math.random() * 30) + 40,
        },
      }));

      setLastUpdated(new Date());
    } catch (error) {
      console.error("Failed to refresh metrics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "healthy":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="w-3 h-3 mr-1" />
            Healthy
          </Badge>
        );
      case "degraded":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <AlertCircle className="w-3 h-3 mr-1" />
            Degraded
          </Badge>
        );
      case "unhealthy":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            <AlertCircle className="w-3 h-3 mr-1" />
            Unhealthy
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <AlertCircle className="w-3 h-3 mr-1" />
            Unknown
          </Badge>
        );
    }
  };

  const getServiceIcon = (service: string) => {
    switch (service) {
      case "redis":
        return <Database className="w-4 h-4" />;
      case "database":
        return <Server className="w-4 h-4" />;
      case "websocket":
        return <Wifi className="w-4 h-4" />;
      case "http":
        return <Activity className="w-4 h-4" />;
      default:
        return <Server className="w-4 h-4" />;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  return (
    <div className="bg-background space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">System Status</h2>
          <p className="text-sm text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshMetrics}
          disabled={isLoading}
        >
          <RefreshCw
            className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              Overall Status
              {getStatusBadge(metrics.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.status === "healthy" ? "✓" : "⚠"}
            </div>
            <p className="text-xs text-muted-foreground">
              All systems operational
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Connected Clients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.metrics.connectedClients}
            </div>
            <p className="text-xs text-muted-foreground">
              Active WebSocket connections
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.metrics.activeAgents}
            </div>
            <p className="text-xs text-muted-foreground">Running in memory</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatUptime(metrics.performance.uptime)}
            </div>
            <p className="text-xs text-muted-foreground">Since last restart</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Services</CardTitle>
            <CardDescription>Status of core system services</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(metrics.services).map(([service, status]) => (
              <div key={service} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getServiceIcon(service)}
                  <span className="capitalize font-medium">{service}</span>
                </div>
                <Badge
                  variant={status === "connected" ? "default" : "destructive"}
                  className={
                    status === "connected"
                      ? "bg-green-100 text-green-800 hover:bg-green-100"
                      : ""
                  }
                >
                  {status === "connected" ? (
                    <CheckCircle className="w-3 h-3 mr-1" />
                  ) : (
                    <AlertCircle className="w-3 h-3 mr-1" />
                  )}
                  {status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>Current system resource usage</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>CPU Usage</span>
                <span>{metrics.performance.cpuUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics.performance.cpuUsage}%` }}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Memory Usage</span>
                <span>{metrics.performance.memoryUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics.performance.memoryUsage}%` }}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Disk Usage</span>
                <span>{metrics.performance.diskUsage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics.performance.diskUsage}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Resource Summary</CardTitle>
          <CardDescription>
            Overview of system resources and components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {metrics.metrics.activeAgents}
              </div>
              <div className="text-sm text-muted-foreground">Active Agents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {metrics.metrics.registeredTools}
              </div>
              <div className="text-sm text-muted-foreground">
                Registered Tools
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {metrics.metrics.enabledProviders}
              </div>
              <div className="text-sm text-muted-foreground">
                Enabled Providers
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {metrics.metrics.connectedClients}
              </div>
              <div className="text-sm text-muted-foreground">
                Connected Clients
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemStatus;
