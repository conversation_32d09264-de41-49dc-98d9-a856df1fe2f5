import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Bot,
  Zap,
  Shield,
  Layers,
  Code,
  Database,
  Cpu,
  Network,
  Settings,
  CheckCircle,
  Star,
  Github,
  Twitter,
  Linkedin,
} from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";

const LandingPage = () => {
  const features = [
    {
      icon: <Bot className="h-8 w-8" />,
      title: "AI Agent Management",
      description:
        "Create, configure, and manage AI agents with different providers and capabilities.",
      color: "text-blue-600",
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Multi-Provider Support",
      description:
        "Seamlessly integrate with OpenAI, Claude, Gemini, Mistral, and Groq APIs.",
      color: "text-yellow-600",
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "Tool Execution System",
      description:
        "Execute internal and external tools with comprehensive schema validation.",
      color: "text-green-600",
    },
    {
      icon: <Database className="h-8 w-8" />,
      title: "Real-time State Management",
      description:
        "Redis-backed state management with WebSocket real-time updates.",
      color: "text-purple-600",
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Enterprise Security",
      description:
        "JWT authentication, rate limiting, and encrypted API key storage.",
      color: "text-red-600",
    },
    {
      icon: "Network",
      title: "Scalable Architecture",
      description:
        "Built with Fastify, Socket.IO, and PostgreSQL for production workloads.",
      color: "text-indigo-600",
    },
  ];

  const techStack = [
    { name: "Fastify", category: "Backend" },
    { name: "Socket.IO", category: "WebSocket" },
    { name: "PostgreSQL", category: "Database" },
    { name: "Redis", category: "Cache" },
    { name: "React", category: "Frontend" },
    { name: "TypeScript", category: "Language" },
    { name: "Tailwind CSS", category: "Styling" },
    { name: "Zod", category: "Validation" },
  ];

  const stats = [
    { label: "API Endpoints", value: "50+" },
    { label: "WebSocket Events", value: "25+" },
    { label: "Database Tables", value: "12" },
    { label: "Provider Integrations", value: "5" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Layers className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold">UAUI Core</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <a
              href="#features"
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Features
            </a>
            <a
              href="#architecture"
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Architecture
            </a>
            <a
              href="#tech-stack"
              className="text-sm font-medium hover:text-primary transition-colors"
            >
              Tech Stack
            </a>
            <ThemeToggle />
            <Link to="/auth/login">
              <Button>Sign In</Button>
            </Link>
          </nav>
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            <div className="flex gap-2">
              <Link to="/auth/login">
                <Button variant="ghost" size="sm">
                  Sign In
                </Button>
              </Link>
              <Link to="/auth/register">
                <Button size="sm">Sign Up</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge variant="outline" className="mb-4">
            <Star className="w-4 h-4 mr-1" />
            Production Ready
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Universal AI Agent
            <br />
            Infrastructure
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            A comprehensive backend system for AI agent interactions with
            multi-provider support, real-time WebSocket communication, and
            enterprise-grade security.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/auth/register">
              <Button size="lg" className="w-full sm:w-auto">
                Get Started Free
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link to="/auth/login">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Sign In
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Powerful Features</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Everything you need to build and deploy AI-powered applications at
              scale.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="border-2 hover:border-primary/20 transition-colors"
              >
                <CardHeader>
                  <div className={`${feature.color} mb-4`}>{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Architecture Section */}
      <section id="architecture" className="py-20 px-4 bg-muted/50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">System Architecture</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Built with modern technologies and best practices for scalability
              and reliability.
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-primary/10 p-3 rounded-lg">
                  <Cpu className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Core Engine</h3>
                  <p className="text-muted-foreground">
                    UAUICoreEngine with router, dispatcher, validator, and
                    APIXIntegrationLayer for seamless WebSocket event handling.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="bg-primary/10 p-3 rounded-lg">
                  <Database className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Data Layer</h3>
                  <p className="text-muted-foreground">
                    PostgreSQL schema with comprehensive indexing and Redis
                    state management for optimal performance.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="bg-primary/10 p-3 rounded-lg">
                  <Network className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    Real-time Communication
                  </h3>
                  <p className="text-muted-foreground">
                    WebSocket-based real-time updates with JWT authentication
                    and comprehensive event broadcasting.
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-card border rounded-lg p-8">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="bg-primary/10 p-4 rounded-lg inline-block mb-2">
                    <Settings className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="font-semibold">UAUI Core Engine</h4>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-muted rounded">
                    <Bot className="h-6 w-6 mx-auto mb-1 text-blue-600" />
                    <div className="text-sm font-medium">Agents</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded">
                    <Code className="h-6 w-6 mx-auto mb-1 text-green-600" />
                    <div className="text-sm font-medium">Tools</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded">
                    <Zap className="h-6 w-6 mx-auto mb-1 text-yellow-600" />
                    <div className="text-sm font-medium">Providers</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded">
                    <Shield className="h-6 w-6 mx-auto mb-1 text-red-600" />
                    <div className="text-sm font-medium">Security</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <section id="tech-stack" className="py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Technology Stack</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Built with industry-leading technologies for performance and
              developer experience.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {techStack.map((tech, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-md transition-shadow"
              >
                <CardContent className="p-6">
                  <div className="font-semibold mb-1">{tech.name}</div>
                  <Badge variant="secondary" className="text-xs">
                    {tech.category}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-primary text-primary-foreground">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Deploy your AI agent infrastructure today and start building the
            future of AI-powered applications.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/auth/register">
              <Button
                size="lg"
                variant="secondary"
                className="w-full sm:w-auto"
              >
                Start Building Today
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link to="/auth/login">
              <Button
                size="lg"
                variant="outline"
                className="w-full sm:w-auto border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
              >
                Sign In to Dashboard
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t bg-muted/50">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Layers className="h-6 w-6 text-primary" />
                <span className="text-lg font-bold">UAUI Core</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Universal AI Agent Infrastructure for modern applications.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  <a
                    href="#features"
                    className="hover:text-primary transition-colors"
                  >
                    Features
                  </a>
                </li>
                <li>
                  <a
                    href="#architecture"
                    className="hover:text-primary transition-colors"
                  >
                    Architecture
                  </a>
                </li>
                <li>
                  <Link
                    to="/auth/login"
                    className="hover:text-primary transition-colors"
                  >
                    Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    API Reference
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Examples
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <div className="flex space-x-4">
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Github className="h-5 w-5" />
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Twitter className="h-5 w-5" />
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; 2024 UAUI Core. Built with ❤️ for the AI community.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
