import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Activity,
  Server,
  Wrench,
  Users,
  Settings,
  Database,
  Layers,
  AlertCircle,
  LogOut,
  User,
} from "lucide-react";
import SystemStatus from "./SystemStatus";
import AgentManager from "./AgentManager";
import ToolRegistry from "./ToolRegistry";
import ProviderConfig from "./ProviderConfig";
import { ThemeToggle } from "./ThemeToggle";

const Home = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    const userData = localStorage.getItem("user_data");
    if (userData) {
      setUser(JSON.parse(userData));
    }

    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get("tab");
    if (tab) {
      setActiveTab(tab);
    }
  }, [location]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`/admin?tab=${value}`, { replace: true });
  };

  const handleLogout = () => {
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_data");
    localStorage.removeItem("remember_me");
    navigate("/auth/login");
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 border-r bg-card p-4 flex flex-col">
        <div className="flex items-center gap-2 mb-6">
          <Layers className="h-6 w-6 text-primary" />
          <h1 className="text-xl font-bold">UAUI Core</h1>
        </div>

        <nav className="space-y-1 flex-1">
          <button
            onClick={() => handleTabChange("dashboard")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "dashboard"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Activity size={18} />
            <span>Dashboard</span>
          </button>
          <button
            onClick={() => handleTabChange("agents")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "agents"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Users size={18} />
            <span>Agents</span>
          </button>
          <button
            onClick={() => handleTabChange("tools")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "tools"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Wrench size={18} />
            <span>Tools</span>
          </button>
          <button
            onClick={() => handleTabChange("providers")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "providers"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Server size={18} />
            <span>Providers</span>
          </button>
          <button
            onClick={() => handleTabChange("logs")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "logs"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Database size={18} />
            <span>Logs</span>
          </button>
          <button
            onClick={() => handleTabChange("settings")}
            className={`flex items-center gap-2 p-2 rounded-md w-full text-left transition-colors ${
              activeTab === "settings"
                ? "bg-accent text-accent-foreground"
                : "hover:bg-accent/50"
            }`}
          >
            <Settings size={18} />
            <span>Settings</span>
          </button>
        </nav>

        <div className="pt-4 mt-auto space-y-4">
          {user && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <User size={16} className="text-muted-foreground" />
                  <span className="text-sm font-medium">{user.name}</span>
                </div>
                <p className="text-xs text-muted-foreground mb-3">
                  {user.email}
                </p>
                <div className="flex items-center justify-between">
                  <ThemeToggle />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLogout}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <LogOut size={16} />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="h-full">
          {activeTab === "dashboard" && (
            <div className="p-6">
              <div className="max-w-7xl mx-auto">
                <div className="flex justify-between items-center mb-6">
                  <h1 className="text-2xl font-bold">Dashboard</h1>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Refresh Data
                  </Button>
                </div>
                <SystemStatus />
              </div>
            </div>
          )}

          {activeTab === "agents" && (
            <div className="h-full">
              <AgentManager />
            </div>
          )}

          {activeTab === "tools" && (
            <div className="h-full">
              <ToolRegistry />
            </div>
          )}

          {activeTab === "providers" && (
            <div className="h-full">
              <ProviderConfig />
            </div>
          )}

          {activeTab === "logs" && (
            <div className="p-6">
              <div className="max-w-7xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>System Logs</CardTitle>
                    <CardDescription>
                      View system logs and activity history
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      Log viewer coming soon...
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === "settings" && (
            <div className="p-6">
              <div className="max-w-7xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>Settings</CardTitle>
                    <CardDescription>
                      Configure system settings and preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      Settings panel coming soon...
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
