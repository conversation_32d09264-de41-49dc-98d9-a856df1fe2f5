import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ProviderStatus {
  name: string;
  status: "online" | "offline" | "error" | "unknown";
  latency: number;
  usageCount: number;
  errorRate: number;
  lastChecked: string;
}

interface ProviderConfig {
  name: string;
  apiKey: string;
  enabled: boolean;
  priority: number;
  fallbackEnabled: boolean;
  models: string[];
  maxRetries: number;
}

const ProviderConfig = () => {
  const [providers, setProviders] = useState<ProviderConfig[]>([
    {
      name: "OpenAI",
      apiKey: "",
      enabled: true,
      priority: 1,
      fallbackEnabled: true,
      models: ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"],
      maxRetries: 3,
    },
    {
      name: "Claude",
      apiKey: "",
      enabled: true,
      priority: 2,
      fallbackEnabled: true,
      models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
      maxRetries: 2,
    },
    {
      name: "Gemini",
      apiKey: "",
      enabled: false,
      priority: 3,
      fallbackEnabled: true,
      models: ["gemini-pro", "gemini-ultra"],
      maxRetries: 2,
    },
    {
      name: "Mistral",
      apiKey: "",
      enabled: false,
      priority: 4,
      fallbackEnabled: true,
      models: ["mistral-large", "mistral-medium", "mistral-small"],
      maxRetries: 2,
    },
    {
      name: "Groq",
      apiKey: "",
      enabled: false,
      priority: 5,
      fallbackEnabled: false,
      models: ["llama2-70b", "mixtral-8x7b"],
      maxRetries: 1,
    },
  ]);

  const [providerStatuses, setProviderStatuses] = useState<ProviderStatus[]>([
    {
      name: "OpenAI",
      status: "online",
      latency: 245,
      usageCount: 1250,
      errorRate: 0.5,
      lastChecked: "2023-06-15T10:30:00Z",
    },
    {
      name: "Claude",
      status: "online",
      latency: 320,
      usageCount: 850,
      errorRate: 0.8,
      lastChecked: "2023-06-15T10:30:00Z",
    },
    {
      name: "Gemini",
      status: "offline",
      latency: 0,
      usageCount: 0,
      errorRate: 0,
      lastChecked: "2023-06-15T10:30:00Z",
    },
    {
      name: "Mistral",
      status: "unknown",
      latency: 0,
      usageCount: 0,
      errorRate: 0,
      lastChecked: "2023-06-15T10:30:00Z",
    },
    {
      name: "Groq",
      status: "error",
      latency: 0,
      usageCount: 0,
      errorRate: 0,
      lastChecked: "2023-06-15T10:30:00Z",
    },
  ]);

  const [activeTab, setActiveTab] = useState("openai");
  const [testingProvider, setTestingProvider] = useState<string | null>(null);

  const handleApiKeyChange = (providerName: string, apiKey: string) => {
    setProviders(
      providers.map((provider) =>
        provider.name === providerName ? { ...provider, apiKey } : provider,
      ),
    );
  };

  const handleToggleEnabled = (providerName: string) => {
    setProviders(
      providers.map((provider) =>
        provider.name === providerName
          ? { ...provider, enabled: !provider.enabled }
          : provider,
      ),
    );
  };

  const handleToggleFallback = (providerName: string) => {
    setProviders(
      providers.map((provider) =>
        provider.name === providerName
          ? { ...provider, fallbackEnabled: !provider.fallbackEnabled }
          : provider,
      ),
    );
  };

  const handlePriorityChange = (providerName: string, priority: number) => {
    setProviders(
      providers.map((provider) =>
        provider.name === providerName ? { ...provider, priority } : provider,
      ),
    );
  };

  const handleMaxRetriesChange = (providerName: string, maxRetries: number) => {
    setProviders(
      providers.map((provider) =>
        provider.name === providerName ? { ...provider, maxRetries } : provider,
      ),
    );
  };

  const testProviderConnection = (providerName: string) => {
    setTestingProvider(providerName);

    // Simulate API call
    setTimeout(() => {
      // Update provider status based on test result
      setProviderStatuses((statuses) =>
        statuses.map((status) =>
          status.name === providerName
            ? {
                ...status,
                status: Math.random() > 0.3 ? "online" : "error",
                lastChecked: new Date().toISOString(),
              }
            : status,
        ),
      );
      setTestingProvider(null);
    }, 2000);
  };

  const saveProviderConfig = () => {
    // Simulate saving configuration
    console.log("Saving provider configuration:", providers);
    // In a real implementation, this would make an API call to save the configuration
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return <Badge className="bg-green-500">Online</Badge>;
      case "offline":
        return <Badge className="bg-gray-500">Offline</Badge>;
      case "error":
        return <Badge className="bg-red-500">Error</Badge>;
      default:
        return <Badge className="bg-yellow-500">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "offline":
        return <XCircle className="h-5 w-5 text-gray-500" />;
      case "error":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  return (
    <div className="bg-background p-6 rounded-lg w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">AI Provider Configuration</h1>
        <p className="text-muted-foreground">
          Configure AI providers, API keys, and fallback logic
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {providerStatuses.map((provider) => (
          <Card key={provider.name} className="bg-card">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">{provider.name}</CardTitle>
                {getStatusBadge(provider.status)}
              </div>
              <CardDescription>
                Last checked:{" "}
                {new Date(provider.lastChecked).toLocaleTimeString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(provider.status)}
                  <span className="text-sm">
                    {provider.status === "online"
                      ? `${provider.latency}ms`
                      : "N/A"}
                  </span>
                </div>
                <div className="text-sm text-right">
                  <div>Usage: {provider.usageCount.toLocaleString()}</div>
                  <div>Error rate: {provider.errorRate}%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="openai">OpenAI</TabsTrigger>
          <TabsTrigger value="claude">Claude</TabsTrigger>
          <TabsTrigger value="gemini">Gemini</TabsTrigger>
          <TabsTrigger value="mistral">Mistral</TabsTrigger>
          <TabsTrigger value="groq">Groq</TabsTrigger>
        </TabsList>

        {providers.map((provider) => {
          const tabValue = provider.name.toLowerCase();
          const status = providerStatuses.find((s) => s.name === provider.name);

          return (
            <TabsContent key={tabValue} value={tabValue} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{provider.name} Configuration</CardTitle>
                  <CardDescription>
                    Configure API keys and settings for {provider.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor={`${tabValue}-api-key`}>API Key</Label>
                    <Input
                      id={`${tabValue}-api-key`}
                      type="password"
                      value={provider.apiKey}
                      onChange={(e) =>
                        handleApiKeyChange(provider.name, e.target.value)
                      }
                      placeholder={`Enter your ${provider.name} API key`}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor={`${tabValue}-enabled`}>
                      Enable Provider
                    </Label>
                    <Switch
                      id={`${tabValue}-enabled`}
                      checked={provider.enabled}
                      onCheckedChange={() => handleToggleEnabled(provider.name)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor={`${tabValue}-fallback`}>
                      Use as Fallback
                    </Label>
                    <Switch
                      id={`${tabValue}-fallback`}
                      checked={provider.fallbackEnabled}
                      onCheckedChange={() =>
                        handleToggleFallback(provider.name)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`${tabValue}-priority`}>Priority</Label>
                    <Select
                      value={provider.priority.toString()}
                      onValueChange={(value) =>
                        handlePriorityChange(provider.name, parseInt(value))
                      }
                    >
                      <SelectTrigger id={`${tabValue}-priority`}>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 (Highest)</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                        <SelectItem value="4">4</SelectItem>
                        <SelectItem value="5">5 (Lowest)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`${tabValue}-retries`}>Max Retries</Label>
                    <Select
                      value={provider.maxRetries.toString()}
                      onValueChange={(value) =>
                        handleMaxRetriesChange(provider.name, parseInt(value))
                      }
                    >
                      <SelectTrigger id={`${tabValue}-retries`}>
                        <SelectValue placeholder="Select max retries" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0</SelectItem>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                        <SelectItem value="5">5</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Available Models</Label>
                    <div className="flex flex-wrap gap-2">
                      {provider.models.map((model) => (
                        <Badge key={model} variant="outline">
                          {model}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">Smart Routing Configuration</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="latencyWeight">Latency Weight</Label>
                        <Input
                          id="latencyWeight"
                          type="number"
                          placeholder="0.4"
                          min="0"
                          max="1"
                          step="0.1"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="costWeight">Cost Weight</Label>
                        <Input
                          id="costWeight"
                          type="number"
                          placeholder="0.3"
                          min="0"
                          max="1"
                          step="0.1"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="reliabilityWeight">
                          Reliability Weight
                        </Label>
                        <Input
                          id="reliabilityWeight"
                          type="number"
                          placeholder="0.3"
                          min="0"
                          max="1"
                          step="0.1"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="loadThreshold">
                          Load Threshold (%)
                        </Label>
                        <Input
                          id="loadThreshold"
                          type="number"
                          placeholder="80"
                          min="1"
                          max="100"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="adaptiveRouting" defaultChecked />
                      <Label htmlFor="adaptiveRouting">
                        Enable adaptive routing
                      </Label>
                    </div>
                  </div>

                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">Usage Limits & Quotas</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="dailyLimit">Daily Request Limit</Label>
                        <Input
                          id="dailyLimit"
                          type="number"
                          placeholder="10000"
                          min="1"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="monthlyBudget">
                          Monthly Budget ($)
                        </Label>
                        <Input
                          id="monthlyBudget"
                          type="number"
                          placeholder="100"
                          min="1"
                          step="0.01"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="rateLimit">Rate Limit (req/min)</Label>
                        <Input
                          id="rateLimit"
                          type="number"
                          placeholder="60"
                          min="1"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="burstLimit">Burst Limit</Label>
                        <Input
                          id="burstLimit"
                          type="number"
                          placeholder="10"
                          min="1"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="autoScaling" />
                      <Label htmlFor="autoScaling">
                        Enable auto-scaling based on demand
                      </Label>
                    </div>
                  </div>

                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">
                      Fallback Chain Configuration
                    </h4>
                    <div className="grid gap-2">
                      <Label htmlFor="fallbackOrder">Fallback Order</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select fallback strategy" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="priority">By Priority</SelectItem>
                          <SelectItem value="latency">By Latency</SelectItem>
                          <SelectItem value="cost">By Cost</SelectItem>
                          <SelectItem value="reliability">
                            By Reliability
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="fallbackDelay">
                          Fallback Delay (ms)
                        </Label>
                        <Input
                          id="fallbackDelay"
                          type="number"
                          placeholder="2000"
                          min="100"
                          max="10000"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="circuitBreakerThreshold">
                          Circuit Breaker Threshold
                        </Label>
                        <Input
                          id="circuitBreakerThreshold"
                          type="number"
                          placeholder="5"
                          min="1"
                          max="20"
                        />
                      </div>
                    </div>
                  </div>

                  {status?.status === "error" && (
                    <Alert className="bg-red-50 border-red-200">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <AlertTitle>Connection Error</AlertTitle>
                      <AlertDescription>
                        Unable to connect to {provider.name} API. Please check
                        your API key and try again.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => testProviderConnection(provider.name)}
                    disabled={testingProvider === provider.name}
                  >
                    {testingProvider === provider.name ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      "Test Connection"
                    )}
                  </Button>
                  <Button onClick={saveProviderConfig}>
                    Save Configuration
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
};

export default ProviderConfig;
