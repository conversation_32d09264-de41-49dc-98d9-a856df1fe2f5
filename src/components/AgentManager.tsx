import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>cle, Edit, Trash2, Check, X, ChevronDown } from "lucide-react";
import { motion } from "framer-motion";

import { But<PERSON> } from "./ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "./ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "./ui/dialog";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Checkbox } from "./ui/checkbox";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Content } from "./ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "./ui/accordion";

interface Agent {
  id: string;
  name: string;
  description: string;
  provider: string;
  hasMemory: boolean;
  tools: string[];
  promptTemplate: string;
  createdAt: string;
  updatedAt: string;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  type: "internal" | "external";
}

interface Provider {
  id: string;
  name: string;
  isActive: boolean;
}

const AgentManager: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: "1",
      name: "General Assistant",
      description: "Default assistant for general queries",
      provider: "openai",
      hasMemory: true,
      tools: ["web-search", "calculator"],
      promptTemplate:
        "You are a helpful assistant that provides accurate information.",
      createdAt: "2023-06-15T10:30:00Z",
      updatedAt: "2023-07-20T14:45:00Z",
    },
    {
      id: "2",
      name: "Code Helper",
      description: "Specialized in programming assistance",
      provider: "claude",
      hasMemory: false,
      tools: ["code-interpreter", "github-access"],
      promptTemplate:
        "You are a coding assistant that helps with programming problems.",
      createdAt: "2023-08-05T09:15:00Z",
      updatedAt: "2023-09-10T11:20:00Z",
    },
  ]);

  const [tools] = useState<Tool[]>([
    {
      id: "1",
      name: "web-search",
      description: "Search the web for information",
      type: "external",
    },
    {
      id: "2",
      name: "calculator",
      description: "Perform mathematical calculations",
      type: "internal",
    },
    {
      id: "3",
      name: "code-interpreter",
      description: "Execute and explain code",
      type: "internal",
    },
    {
      id: "4",
      name: "github-access",
      description: "Access GitHub repositories",
      type: "external",
    },
    {
      id: "5",
      name: "weather-api",
      description: "Get weather information",
      type: "external",
    },
  ]);

  const [providers] = useState<Provider[]>([
    { id: "1", name: "openai", isActive: true },
    { id: "2", name: "claude", isActive: true },
    { id: "3", name: "gemini", isActive: true },
    { id: "4", name: "mistral", isActive: false },
    { id: "5", name: "groq", isActive: true },
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<Agent | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    provider: "",
    hasMemory: false,
    promptTemplate: "",
  });

  useEffect(() => {
    if (currentAgent && isEditing) {
      setFormData({
        name: currentAgent.name,
        description: currentAgent.description,
        provider: currentAgent.provider,
        hasMemory: currentAgent.hasMemory,
        promptTemplate: currentAgent.promptTemplate,
      });
      setSelectedTools(currentAgent.tools);
    } else if (!isEditing) {
      setFormData({
        name: "",
        description: "",
        provider: "",
        hasMemory: false,
        promptTemplate: "",
      });
      setSelectedTools([]);
    }
  }, [currentAgent, isEditing]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, hasMemory: checked }));
  };

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, provider: value }));
  };

  const handleToolToggle = (toolId: string) => {
    setSelectedTools((prev) =>
      prev.includes(toolId)
        ? prev.filter((id) => id !== toolId)
        : [...prev, toolId],
    );
  };

  const handleCreateAgent = () => {
    const newAgent: Agent = {
      id: Date.now().toString(),
      name: formData.name,
      description: formData.description,
      provider: formData.provider,
      hasMemory: formData.hasMemory,
      tools: selectedTools,
      promptTemplate: formData.promptTemplate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setAgents((prev) => [...prev, newAgent]);
    setIsDialogOpen(false);
  };

  const handleUpdateAgent = () => {
    if (!currentAgent) return;

    const updatedAgent: Agent = {
      ...currentAgent,
      name: formData.name,
      description: formData.description,
      provider: formData.provider,
      hasMemory: formData.hasMemory,
      tools: selectedTools,
      promptTemplate: formData.promptTemplate,
      updatedAt: new Date().toISOString(),
    };

    setAgents((prev) =>
      prev.map((agent) =>
        agent.id === currentAgent.id ? updatedAgent : agent,
      ),
    );
    setIsDialogOpen(false);
    setIsEditing(false);
    setCurrentAgent(null);
  };

  const handleDeleteAgent = () => {
    if (!currentAgent) return;

    setAgents((prev) => prev.filter((agent) => agent.id !== currentAgent.id));
    setIsDeleteDialogOpen(false);
    setCurrentAgent(null);
  };

  const openCreateDialog = () => {
    setIsEditing(false);
    setCurrentAgent(null);
    setIsDialogOpen(true);
  };

  const openEditDialog = (agent: Agent) => {
    setIsEditing(true);
    setCurrentAgent(agent);
    setIsDialogOpen(true);
  };

  const openDeleteDialog = (agent: Agent) => {
    setCurrentAgent(agent);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="bg-background p-6 rounded-lg shadow-sm w-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Agent Manager</h1>
          <p className="text-muted-foreground">
            Create and manage AI agents with custom configurations
          </p>
        </div>
        <Button onClick={openCreateDialog} className="flex items-center gap-2">
          <PlusCircle className="h-4 w-4" />
          Create Agent
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Agents</CardTitle>
          <CardDescription>
            Manage your AI agents and their configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Memory</TableHead>
                <TableHead>Tools</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {agents.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{agent.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {agent.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {agent.provider}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {agent.hasMemory ? (
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        Enabled
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-muted-foreground"
                      >
                        Disabled
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {agent.tools.map((tool) => (
                        <Badge
                          key={tool}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(agent.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(agent)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDeleteDialog(agent)}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {agents.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={6}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No agents found. Create your first agent to get started.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create/Edit Agent Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {isEditing ? "Edit Agent" : "Create New Agent"}
            </DialogTitle>
            <DialogDescription>
              {isEditing
                ? "Update the configuration for this agent."
                : "Configure a new AI agent with custom settings."}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="basic">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
              <TabsTrigger value="prompt">Prompt Template</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Agent name"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe what this agent does"
                    value={formData.description}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="provider">AI Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={handleSelectChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a provider" />
                    </SelectTrigger>
                    <SelectContent>
                      {providers
                        .filter((provider) => provider.isActive)
                        .map((provider) => (
                          <SelectItem key={provider.id} value={provider.name}>
                            {provider.name.charAt(0).toUpperCase() +
                              provider.name.slice(1)}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="memory"
                    checked={formData.hasMemory}
                    onCheckedChange={handleCheckboxChange}
                  />
                  <Label htmlFor="memory">Enable memory</Label>
                </div>

                {formData.hasMemory && (
                  <div className="grid gap-4 p-4 border rounded-lg bg-muted/50">
                    <h4 className="font-medium">Memory Configuration</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="memoryTtl">Memory TTL (hours)</Label>
                        <Input
                          id="memoryTtl"
                          type="number"
                          placeholder="24"
                          min="1"
                          max="168"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="maxTokens">Max Memory Tokens</Label>
                        <Input
                          id="maxTokens"
                          type="number"
                          placeholder="4000"
                          min="100"
                          max="32000"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="persistentMemory" />
                      <Label htmlFor="persistentMemory">
                        Persistent memory across sessions
                      </Label>
                    </div>
                  </div>
                )}

                <div className="grid gap-4 p-4 border rounded-lg">
                  <h4 className="font-medium">Session Configuration</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="maxSessions">
                        Max Concurrent Sessions
                      </Label>
                      <Input
                        id="maxSessions"
                        type="number"
                        placeholder="10"
                        min="1"
                        max="100"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="sessionTtl">Session TTL (minutes)</Label>
                      <Input
                        id="sessionTtl"
                        type="number"
                        placeholder="60"
                        min="5"
                        max="1440"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="allowConcurrent" defaultChecked />
                    <Label htmlFor="allowConcurrent">
                      Allow concurrent sessions
                    </Label>
                  </div>
                </div>

                <div className="grid gap-4 p-4 border rounded-lg">
                  <h4 className="font-medium">Streaming Configuration</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="enableStreaming" defaultChecked />
                    <Label htmlFor="enableStreaming">
                      Enable real-time streaming
                    </Label>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="chunkSize">Chunk Size (characters)</Label>
                      <Input
                        id="chunkSize"
                        type="number"
                        placeholder="50"
                        min="10"
                        max="500"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="bufferTimeout">Buffer Timeout (ms)</Label>
                      <Input
                        id="bufferTimeout"
                        type="number"
                        placeholder="100"
                        min="50"
                        max="1000"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tools" className="py-4">
              <div className="grid gap-4">
                <Label>Available Tools</Label>
                <div className="border rounded-md">
                  <Accordion type="multiple" className="w-full">
                    {tools.map((tool) => (
                      <AccordionItem key={tool.id} value={tool.id}>
                        <div className="flex items-center px-4">
                          <Checkbox
                            id={`tool-${tool.id}`}
                            checked={selectedTools.includes(tool.name)}
                            onCheckedChange={() => handleToolToggle(tool.name)}
                            className="mr-3"
                          />
                          <AccordionTrigger className="py-2 hover:no-underline">
                            <div className="flex items-center">
                              <span>{tool.name}</span>
                              <Badge variant="outline" className="ml-2 text-xs">
                                {tool.type}
                              </Badge>
                            </div>
                          </AccordionTrigger>
                        </div>
                        <AccordionContent className="px-12 pb-3 text-sm text-muted-foreground">
                          {tool.description}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="prompt" className="py-4">
              <div className="grid gap-4">
                <Label htmlFor="promptTemplate">Prompt Template</Label>
                <Textarea
                  id="promptTemplate"
                  name="promptTemplate"
                  placeholder="Enter the system prompt template for this agent"
                  value={formData.promptTemplate}
                  onChange={handleInputChange}
                  className="min-h-[200px] font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground">
                  Define the system prompt that will guide the agent's behavior
                  and responses.
                </p>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={isEditing ? handleUpdateAgent : handleCreateAgent}>
              {isEditing ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Agent</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the agent "{currentAgent?.name}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteAgent}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentManager;
